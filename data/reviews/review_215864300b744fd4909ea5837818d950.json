{"id": "review_215864300b744fd4909ea5837818d950", "templateId": "template_demo_code_review", "templateVersion": "1.0", "type": "code-review", "createdTime": "2025-08-03 16:19:25", "status": "IN_PROGRESS", "reviewItems": [{"itemId": "item_001", "sequence": 1, "content": "代码是否遵循团队编码规范？", "category": "代码规范", "required": true, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}, {"itemId": "item_002", "sequence": 2, "content": "是否有适当的注释和文档？", "category": "文档", "required": true, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}, {"itemId": "item_003", "sequence": 3, "content": "单元测试覆盖率是否达标？", "category": "测试", "required": true, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}, {"itemId": "item_004", "sequence": 4, "content": "是否存在潜在的安全漏洞？", "category": "安全", "required": true, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}, {"itemId": "item_005", "sequence": 5, "content": "代码性能是否满足要求？", "category": "性能", "required": false, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}, {"itemId": "item_006", "sequence": 6, "content": "是否有重复代码需要重构？", "category": "重构", "required": false, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}, {"itemId": "item_007", "sequence": 7, "content": "异常处理是否完善？", "category": "异常处理", "required": true, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}, {"itemId": "item_008", "sequence": 8, "content": "数据库操作是否安全？", "category": "数据库", "required": true, "status": "PENDING", "comment": "", "reviewHistory": [], "customFields": null}]}
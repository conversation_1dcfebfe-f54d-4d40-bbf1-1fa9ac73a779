package com.fasnote.alm.checklist.repository;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * JSON 文件存储仓库接口
 * 提供通用的 CRUD 操作方法
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
public interface JsonFileRepository<T, ID> {
    
    /**
     * 保存实体
     * 
     * @param entity 要保存的实体
     * @return 保存后的实体
     * @throws IOException IO异常
     */
    T save(T entity) throws IOException;
    
    /**
     * 根据ID查找实体
     * 
     * @param id 实体ID
     * @return 实体的Optional包装
     * @throws IOException IO异常
     */
    Optional<T> findById(ID id) throws IOException;
    
    /**
     * 查找所有实体
     * 
     * @return 所有实体的列表
     * @throws IOException IO异常
     */
    List<T> findAll() throws IOException;
    
    /**
     * 检查实体是否存在
     * 
     * @param id 实体ID
     * @return 是否存在
     * @throws IOException IO异常
     */
    boolean existsById(ID id) throws IOException;
    
    /**
     * 根据ID删除实体
     * 
     * @param id 实体ID
     * @throws IOException IO异常
     */
    void deleteById(ID id) throws IOException;
    
    /**
     * 删除实体
     * 
     * @param entity 要删除的实体
     * @throws IOException IO异常
     */
    void delete(T entity) throws IOException;
    
    /**
     * 删除所有实体
     * 
     * @throws IOException IO异常
     */
    void deleteAll() throws IOException;
    
    /**
     * 统计实体数量
     * 
     * @return 实体数量
     * @throws IOException IO异常
     */
    long count() throws IOException;
}
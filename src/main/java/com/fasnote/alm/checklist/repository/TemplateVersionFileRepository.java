package com.fasnote.alm.checklist.repository;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.fasnote.alm.checklist.model.ReviewTemplateVersion;
import com.fasnote.alm.checklist.model.TemplateSnapshot;
import com.fasnote.alm.checklist.util.JsonFileUtil;

/**
 * 模板版本文件存储实现
 */
@Repository("templateVersionFileRepository")
public class TemplateVersionFileRepository implements TemplateVersionRepository {
    
    @Autowired
    private JsonFileUtil jsonFileUtil;
    
    private static final String BASE_SNAPSHOTS_DIR = "data/template-snapshots";
    private static final String BASE_REVIEW_VERSIONS_DIR = "data/review-template-versions";
    private static final String FILE_EXTENSION = ".json";
    
    /**
     * 获取项目快照目录
     */
    private String getProjectSnapshotsDir(String projectId) {
        if (projectId == null || projectId.trim().isEmpty()) {
            return BASE_SNAPSHOTS_DIR; // 向后兼容：无项目ID时使用基础目录
        }
        return BASE_SNAPSHOTS_DIR + "/" + projectId;
    }

    /**
     * 获取项目版本关联目录
     */
    private String getProjectReviewVersionsDir(String projectId) {
        if (projectId == null || projectId.trim().isEmpty()) {
            return BASE_REVIEW_VERSIONS_DIR; // 向后兼容：无项目ID时使用基础目录
        }
        return BASE_REVIEW_VERSIONS_DIR + "/" + projectId;
    }

    @Override
    public TemplateSnapshot saveSnapshot(String projectId, TemplateSnapshot snapshot) throws IOException {
        String fileName = snapshot.getId() + FILE_EXTENSION;
        String snapshotsDir = getProjectSnapshotsDir(projectId);
        jsonFileUtil.saveToFile(snapshotsDir, fileName, snapshot);
        return snapshot;
    }
    
    @Override
    public Optional<TemplateSnapshot> findSnapshotById(String projectId, String snapshotId) throws IOException {
        try {
            String fileName = snapshotId + FILE_EXTENSION;
            String snapshotsDir = getProjectSnapshotsDir(projectId);
            TemplateSnapshot snapshot = jsonFileUtil.loadFromFile(snapshotsDir, fileName, TemplateSnapshot.class);
            return Optional.ofNullable(snapshot);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<TemplateSnapshot> findSnapshotsByTemplateId(String projectId, String templateId, int limit, int offset) throws IOException {
        String snapshotsDir = getProjectSnapshotsDir(projectId);
        List<TemplateSnapshot> allSnapshots = jsonFileUtil.loadAllFromDirectory(snapshotsDir, TemplateSnapshot.class);
        
        // 过滤指定模板的快照
        List<TemplateSnapshot> filteredSnapshots = allSnapshots.stream()
            .filter(snapshot -> templateId == null || templateId.equals(snapshot.getTemplateId()))
            .sorted((a, b) -> b.getCreatedTime().compareTo(a.getCreatedTime()))
            .collect(Collectors.toList());
        
        // 分页
        int endIndex = Math.min(offset + limit, filteredSnapshots.size());
        if (offset >= filteredSnapshots.size()) {
            return List.of();
        }
        
        return filteredSnapshots.subList(offset, endIndex);
    }
    
    @Override
    public boolean deleteSnapshot(String projectId, String snapshotId) throws IOException {
        String fileName = snapshotId + FILE_EXTENSION;
        String snapshotsDir = getProjectSnapshotsDir(projectId);
        return jsonFileUtil.deleteFile(snapshotsDir, fileName);
    }
    
    @Override
    public ReviewTemplateVersion saveReviewTemplateVersion(String projectId, ReviewTemplateVersion reviewVersion) throws IOException {
        String fileName = reviewVersion.getReviewId() + FILE_EXTENSION;
        String reviewVersionsDir = getProjectReviewVersionsDir(projectId);
        jsonFileUtil.saveToFile(reviewVersionsDir, fileName, reviewVersion);
        return reviewVersion;
    }

    @Override
    public Optional<ReviewTemplateVersion> findReviewTemplateVersionByReviewId(String projectId, String reviewId) throws IOException {
        try {
            String fileName = reviewId + FILE_EXTENSION;
            String reviewVersionsDir = getProjectReviewVersionsDir(projectId);
            ReviewTemplateVersion reviewVersion = jsonFileUtil.loadFromFile(reviewVersionsDir, fileName, ReviewTemplateVersion.class);
            return Optional.ofNullable(reviewVersion);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    public boolean deleteReviewTemplateVersion(String projectId, String reviewId) throws IOException {
        String fileName = reviewId + FILE_EXTENSION;
        String reviewVersionsDir = getProjectReviewVersionsDir(projectId);
        return jsonFileUtil.deleteFile(reviewVersionsDir, fileName);
    }
    
    @Override
    public long countSnapshots(String projectId, String templateId) throws IOException {
        String snapshotsDir = getProjectSnapshotsDir(projectId);
        List<TemplateSnapshot> allSnapshots = jsonFileUtil.loadAllFromDirectory(snapshotsDir, TemplateSnapshot.class);

        if (templateId == null) {
            return allSnapshots.size();
        }

        return allSnapshots.stream()
            .filter(snapshot -> templateId.equals(snapshot.getTemplateId()))
            .count();
    }

    @Override
    public boolean existsSnapshot(String projectId, String snapshotId) throws IOException {
        String fileName = snapshotId + FILE_EXTENSION;
        String snapshotsDir = getProjectSnapshotsDir(projectId);
        return jsonFileUtil.fileExists(snapshotsDir, fileName);
    }

    @Override
    public boolean existsReviewTemplateVersion(String projectId, String reviewId) throws IOException {
        String fileName = reviewId + FILE_EXTENSION;
        String reviewVersionsDir = getProjectReviewVersionsDir(projectId);
        return jsonFileUtil.fileExists(reviewVersionsDir, fileName);
    }
}

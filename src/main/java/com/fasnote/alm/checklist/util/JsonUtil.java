package com.fasnote.alm.checklist.util;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

/**
 * JSON 序列化和反序列化工具类
 */
public class JsonUtil {
    
    private static final ObjectMapper objectMapper;
    
    static {
        objectMapper = new ObjectMapper();
        // 注册 Java 8 时间模块
        objectMapper.registerModule(new JavaTimeModule());
        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 启用缩进输出
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
    }
    
    /**
     * 获取 ObjectMapper 实例
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }
    
    /**
     * 将对象序列化为 JSON 字符串
     * 
     * @param object 要序列化的对象
     * @return JSON 字符串
     * @throws JsonProcessingException 序列化异常
     */
    public static String toJson(Object object) throws JsonProcessingException {
        if (object == null) {
            return null;
        }
        return objectMapper.writeValueAsString(object);
    }
    
    /**
     * 将 JSON 字符串反序列化为指定类型的对象
     * 
     * @param json JSON 字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 反序列化后的对象
     * @throws JsonProcessingException 反序列化异常
     */
    public static <T> T fromJson(String json, Class<T> clazz) throws JsonProcessingException {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        return objectMapper.readValue(json, clazz);
    }
    
    /**
     * 将 JSON 字符串反序列化为指定类型的对象（支持泛型）
     * 
     * @param json JSON 字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 反序列化后的对象
     * @throws JsonProcessingException 反序列化异常
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) throws JsonProcessingException {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        return objectMapper.readValue(json, typeReference);
    }
    
    /**
     * 将对象转换为另一种类型（通过 JSON 序列化/反序列化）
     * 
     * @param fromValue 源对象
     * @param toValueType 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     * @throws IllegalArgumentException 转换异常
     */
    public static <T> T convertValue(Object fromValue, Class<T> toValueType) throws IllegalArgumentException {
        if (fromValue == null) {
            return null;
        }
        return objectMapper.convertValue(fromValue, toValueType);
    }
    
    /**
     * 将对象转换为另一种类型（通过 JSON 序列化/反序列化，支持泛型）
     * 
     * @param fromValue 源对象
     * @param toValueTypeRef 目标类型引用
     * @param <T> 泛型类型
     * @return 转换后的对象
     * @throws IllegalArgumentException 转换异常
     */
    public static <T> T convertValue(Object fromValue, TypeReference<T> toValueTypeRef) throws IllegalArgumentException {
        if (fromValue == null) {
            return null;
        }
        return objectMapper.convertValue(fromValue, toValueTypeRef);
    }
    
    /**
     * 检查 JSON 字符串是否有效
     * 
     * @param json JSON 字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            objectMapper.readTree(json);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 格式化 JSON 字符串（美化输出）
     * 
     * @param json 原始 JSON 字符串
     * @return 格式化后的 JSON 字符串
     * @throws JsonProcessingException 处理异常
     */
    public static String prettyPrint(String json) throws JsonProcessingException {
        if (json == null || json.trim().isEmpty()) {
            return json;
        }
        Object jsonObject = objectMapper.readValue(json, Object.class);
        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObject);
    }
}
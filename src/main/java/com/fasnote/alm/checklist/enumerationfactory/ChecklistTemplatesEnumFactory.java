package com.fasnote.alm.checklist.enumerationfactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Properties;

import org.jetbrains.annotations.NotNull;

import com.fasnote.alm.checklist.config.WebConfig;
import com.fasnote.alm.checklist.model.ChecklistTemplate;
import com.fasnote.alm.checklist.service.ChecklistTemplateService;
import com.polarion.platform.persistence.IEnumOption;
import com.polarion.platform.persistence.IEnumeration;
import com.polarion.platform.persistence.model.IPObject;
import com.polarion.platform.persistence.spi.AbstractObjectEnumFactory;
import com.polarion.platform.persistence.spi.AbstractObjectEnumeration;
import com.polarion.platform.persistence.spi.EnumOption;
import com.polarion.subterra.base.data.identification.IContextId;

public class ChecklistTemplatesEnumFactory extends AbstractObjectEnumFactory {

	@Override
	public String getName() {
		return "Checklist 模版";
	}

	@Override
	public String getOptionalFieldName() {
		return "type";
	}

	@Override
	public IEnumeration getEnumeration(final String enumId, final IContextId contextId) {
		final String query = extractValueFromEnumId(enumId);
		final String projectId = contextId.getContextName();
		return new AbstractObjectEnumeration(enumId) {

			@Override
			public IEnumOption wrapOption(String optionId) {
				ChecklistTemplateService checklistTemplateService = WebConfig.getSpringContext()
						.getBean(ChecklistTemplateService.class);
				Optional<ChecklistTemplate> templateOpt;
				try {
					templateOpt = checklistTemplateService.getTemplateById(optionId);
					if (templateOpt.isEmpty()) {
						return createPhantomOption(enumId, optionId);
					}
					return wrapTemplate(enumId, templateOpt.get());
				} catch (IOException e) {
					return createPhantomOption(enumId, optionId);
				}

			}

			@Override
			public List getAvailableOptions(Object controlValue, IEnumOption currentValue) {
				List<IEnumOption> options = new ArrayList<>();
                List<ChecklistTemplate> timePoints = getTimePoints(query, contextId);
                for (ChecklistTemplate template : timePoints) {
                    options.add(wrapTemplate(enumId, template));
                }
                return options;
			}

			@Override
			@NotNull
			public IEnumOption wrapObject(@NotNull IPObject object) {
				throw new IllegalArgumentException();
			}

		};
	}

	protected List<ChecklistTemplate> getTimePoints(String type, IContextId contextId){
		ChecklistTemplateService checklistTemplateService = WebConfig.getSpringContext()
				.getBean(ChecklistTemplateService.class);
		try {
			return checklistTemplateService.getTemplatesByType(type);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	protected IEnumOption wrapTemplate(String enumId, ChecklistTemplate template) {
		return new EnumOption(enumId, template.getId(), template.getName(), 0, false, new Properties());
	}

}

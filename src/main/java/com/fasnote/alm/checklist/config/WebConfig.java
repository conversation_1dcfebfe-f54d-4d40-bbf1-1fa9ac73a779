package com.fasnote.alm.checklist.config;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.fasnote.alm.checklist.controller.ChecklistReviewController;
import com.fasnote.alm.checklist.controller.ChecklistTemplateController;
import com.fasnote.alm.checklist.controller.HealthController;
import com.fasnote.alm.checklist.controller.TemplateVersionController;
import com.fasnote.alm.checklist.exception.GlobalExceptionHandler;
import com.fasnote.alm.checklist.repository.ChecklistReviewRepository;
import com.fasnote.alm.checklist.repository.ChecklistTemplateRepository;
import com.fasnote.alm.checklist.service.ChecklistReviewService;
import com.fasnote.alm.checklist.service.ChecklistTemplateService;
import com.fasnote.alm.checklist.service.DefectGenerationService;
import com.fasnote.alm.checklist.service.FileStorageService;
import com.fasnote.alm.checklist.service.TemplateVersionService;
import com.fasnote.alm.checklist.util.JsonFileUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Spring MVC Web Configuration
 * 显式声明所有Bean，替代组件扫描
 */
@Configuration
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer , ApplicationContextAware {

	public static ApplicationContext springContext;

	public static ApplicationContext getSpringContext() {
		return springContext;
	}

	@SuppressWarnings("static-access")
	public void setApplicationContext(ApplicationContext springContext) {
		this.springContext = springContext;
	}
	
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 配置API端点的CORS
        registry.addMapping("/api/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*");

        // 配置健康检查端点的CORS
        registry.addMapping("/health")
                .allowedOrigins("*")
                .allowedMethods("GET", "OPTIONS")
                .allowedHeaders("*");
    }

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    // Repository层Bean声明
    @Bean
    public ChecklistTemplateRepository checklistTemplateRepository() {
        return new ChecklistTemplateRepository();
    }

    @Bean
    public ChecklistReviewRepository checklistReviewRepository() {
        return new ChecklistReviewRepository();
    }

    // Service层Bean声明
    @Bean
    public FileStorageService fileStorageService() {
        return new FileStorageService();
    }

    @Bean
    public ChecklistTemplateService checklistTemplateService() {
        return new ChecklistTemplateService(
            checklistTemplateRepository()
        );
    }

    @Bean
    public DefectGenerationService defectGenerationService() {
        return new DefectGenerationService();
    }

    @Bean
    public ChecklistReviewService checklistReviewService() {
        return new ChecklistReviewService(
            checklistReviewRepository(),
            checklistTemplateRepository(),
            defectGenerationService()
        );
    }

    @Bean
    public JsonFileUtil jsonFileUtil() {
        return new JsonFileUtil();
    }

    @Bean
    public TemplateVersionService templateVersionService() {
        return new TemplateVersionService();
    }

    // Controller层Bean声明
    @Bean
    public ChecklistTemplateController checklistTemplateController() {
        return new ChecklistTemplateController(checklistTemplateService());
    }

    @Bean
    public ChecklistReviewController checklistReviewController() {
        return new ChecklistReviewController(checklistReviewService());
    }

    @Bean
    public HealthController healthController() {
        return new HealthController();
    }

    @Bean
    public TemplateVersionController templateVersionController() {
        return new TemplateVersionController();
    }

    // Exception Handler Bean声明
    @Bean
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }
}
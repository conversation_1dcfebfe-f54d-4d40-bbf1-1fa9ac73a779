package com.fasnote.alm.checklist.model;

/**
 * 缺陷条件
 */
public class DefectCondition {
    private String field;
    private String operator;
    private Object value;
    private String logicOperator;
    
    // 构造函数
    public DefectCondition() {}
    
    public DefectCondition(String field, String operator, Object value) {
        this.field = field;
        this.operator = operator;
        this.value = value;
    }
    
    // Getters and Setters
    public String getField() { return field; }
    public void setField(String field) { this.field = field; }
    
    public String getOperator() { return operator; }
    public void setOperator(String operator) { this.operator = operator; }
    
    public Object getValue() { return value; }
    public void setValue(Object value) { this.value = value; }
    
    public String getLogicOperator() { return logicOperator; }
    public void setLogicOperator(String logicOperator) { this.logicOperator = logicOperator; }
    
    /**
     * 支持的操作符
     */
    public enum Operator {
        EQUALS("equals"),
        CONTAINS("contains"),
        STARTS_WITH("startsWith"),
        ENDS_WITH("endsWith"),
        REGEX("regex"),
        EXISTS("exists"),
        NOT_EXISTS("notExists");
        
        private final String value;
        
        Operator(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        public static Operator fromValue(String value) {
            for (Operator op : values()) {
                if (op.value.equals(value)) {
                    return op;
                }
            }
            throw new IllegalArgumentException("Unknown operator: " + value);
        }
    }
    
    /**
     * 逻辑操作符
     */
    public enum LogicOperator {
        AND("AND"),
        OR("OR");
        
        private final String value;
        
        LogicOperator(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        public static LogicOperator fromValue(String value) {
            for (LogicOperator op : values()) {
                if (op.value.equals(value)) {
                    return op;
                }
            }
            throw new IllegalArgumentException("Unknown logic operator: " + value);
        }
    }
}

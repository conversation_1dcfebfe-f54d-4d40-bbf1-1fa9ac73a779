package com.fasnote.alm.checklist.model;

import java.time.LocalDateTime;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 评审记录数据模型
 */
public class ReviewRecord {
    
    @JsonProperty("reviewer")
    private String reviewer;
    
    @JsonProperty("reviewTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;
    
    @JsonProperty("status")
    private ReviewStatus status;
    
    @JsonProperty("comment")
    private String comment;
    
    // 默认构造函数
    public ReviewRecord() {}
    
    // 全参构造函数
    public ReviewRecord(String reviewer, LocalDateTime reviewTime, 
                       ReviewStatus status, String comment) {
        this.reviewer = reviewer;
        this.reviewTime = reviewTime;
        this.status = status;
        this.comment = comment;
    }
    
    // Getters and Setters
    public String getReviewer() {
        return reviewer;
    }
    
    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }
    
    public LocalDateTime getReviewTime() {
        return reviewTime;
    }
    
    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }
    
    public ReviewStatus getStatus() {
        return status;
    }
    
    public void setStatus(ReviewStatus status) {
        this.status = status;
    }
    
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReviewRecord that = (ReviewRecord) o;
        return Objects.equals(reviewer, that.reviewer) &&
               Objects.equals(reviewTime, that.reviewTime) &&
               status == that.status;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(reviewer, reviewTime, status);
    }
    
    @Override
    public String toString() {
        return "ReviewRecord{" +
               "reviewer='" + reviewer + '\'' +
               ", reviewTime=" + reviewTime +
               ", status=" + status +
               ", comment='" + comment + '\'' +
               '}';
    }
}
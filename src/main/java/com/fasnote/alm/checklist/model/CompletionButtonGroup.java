package com.fasnote.alm.checklist.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 完成评审按钮组配置
 */
public class CompletionButtonGroup {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("buttons")
    private List<CompletionButton> buttons;
    
    @JsonProperty("scriptContext")
    private ScriptContext scriptContext;
    
    // 构造函数
    public CompletionButtonGroup() {}
    
    public CompletionButtonGroup(String id, String name, String description, 
                                List<CompletionButton> buttons, ScriptContext scriptContext) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.buttons = buttons;
        this.scriptContext = scriptContext;
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public List<CompletionButton> getButtons() { return buttons; }
    public void setButtons(List<CompletionButton> buttons) { this.buttons = buttons; }
    
    public ScriptContext getScriptContext() { return scriptContext; }
    public void setScriptContext(ScriptContext scriptContext) { this.scriptContext = scriptContext; }
    
    /**
     * 脚本执行上下文配置
     */
    public static class ScriptContext {
        @JsonProperty("allowedFunctions")
        private List<String> allowedFunctions;
        
        @JsonProperty("timeout")
        private int timeout;
        
        public ScriptContext() {}
        
        public ScriptContext(List<String> allowedFunctions, int timeout) {
            this.allowedFunctions = allowedFunctions;
            this.timeout = timeout;
        }
        
        public List<String> getAllowedFunctions() { return allowedFunctions; }
        public void setAllowedFunctions(List<String> allowedFunctions) { this.allowedFunctions = allowedFunctions; }
        
        public int getTimeout() { return timeout; }
        public void setTimeout(int timeout) { this.timeout = timeout; }
    }
}

package com.fasnote.alm.checklist.model;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 评审状态枚举
 */
public enum ReviewStatus {
    PASS("PASS", "通过"),
    FAIL("FAIL", "不通过"),
    SKIP("SKIP", "暂不处理"),
    PENDING("PENDING", "待处理");
    
    private final String code;
    private final String description;
    
    ReviewStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    @JsonValue
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static ReviewStatus fromCode(String code) {
        for (ReviewStatus status : ReviewStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown review status code: " + code);
    }
    
    @Override
    public String toString() {
        return code;
    }
}
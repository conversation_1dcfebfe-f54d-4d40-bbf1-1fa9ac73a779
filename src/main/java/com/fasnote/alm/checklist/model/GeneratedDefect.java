package com.fasnote.alm.checklist.model;

import java.time.LocalDateTime;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 生成的缺陷信息
 */
public class GeneratedDefect {
    private String id;
    private String title;
    private String description;
    private String severity;
    private String category;
    
    @JsonProperty("sourceItemId")
    private String sourceItemId;
    
    @JsonProperty("sourceContent")
    private String sourceContent;
    
    @JsonProperty("customData")
    private Map<String, Object> customData;
    
    @JsonProperty("createdTime")
    private LocalDateTime createdTime;
    
    private DefectStatus status;
    
    // 构造函数
    public GeneratedDefect() {
        this.createdTime = LocalDateTime.now();
        this.status = DefectStatus.DRAFT;
    }
    
    public GeneratedDefect(String id, String title, String description, String severity, String category) {
        this();
        this.id = id;
        this.title = title;
        this.description = description;
        this.severity = severity;
        this.category = category;
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getSeverity() { return severity; }
    public void setSeverity(String severity) { this.severity = severity; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public String getSourceItemId() { return sourceItemId; }
    public void setSourceItemId(String sourceItemId) { this.sourceItemId = sourceItemId; }
    
    public String getSourceContent() { return sourceContent; }
    public void setSourceContent(String sourceContent) { this.sourceContent = sourceContent; }
    
    public Map<String, Object> getCustomData() { return customData; }
    public void setCustomData(Map<String, Object> customData) { this.customData = customData; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public DefectStatus getStatus() { return status; }
    public void setStatus(DefectStatus status) { this.status = status; }
    
    /**
     * 缺陷状态
     */
    public enum DefectStatus {
        DRAFT("draft"),
        CONFIRMED("confirmed"),
        SUBMITTED("submitted");
        
        private final String value;
        
        DefectStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        public static DefectStatus fromValue(String value) {
            for (DefectStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown defect status: " + value);
        }
    }
}

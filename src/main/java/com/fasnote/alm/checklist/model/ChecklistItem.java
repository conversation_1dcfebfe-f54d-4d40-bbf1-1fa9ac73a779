package com.fasnote.alm.checklist.model;

import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 检查项数据模型
 */
public class ChecklistItem {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("sequence")
    private Integer sequence;
    
    @JsonProperty("content")
    private String content;
    
    @JsonProperty("required")
    private Boolean required;
    
    @JsonProperty("category")
    private String category;
    
    // 默认构造函数
    public ChecklistItem() {}
    
    // 全参构造函数
    public ChecklistItem(String id, Integer sequence, String content, 
                        Boolean required, String category) {
        this.id = id;
        this.sequence = sequence;
        this.content = content;
        this.required = required;
        this.category = category;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public Integer getSequence() {
        return sequence;
    }
    
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Boolean getRequired() {
        return required;
    }
    
    public void setRequired(Boolean required) {
        this.required = required;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChecklistItem that = (ChecklistItem) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(sequence, that.sequence) &&
               Objects.equals(content, that.content);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, sequence, content);
    }
    
    @Override
    public String toString() {
        return "ChecklistItem{" +
               "id='" + id + '\'' +
               ", sequence=" + sequence +
               ", content='" + content + '\'' +
               ", required=" + required +
               ", category='" + category + '\'' +
               '}';
    }
}
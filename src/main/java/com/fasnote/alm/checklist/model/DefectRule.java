package com.fasnote.alm.checklist.model;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 缺陷生成规则配置
 */
public class DefectRule {
    private String id;
    private String name;
    private String description;
    private boolean enabled;
    
    @JsonProperty("trigger")
    private DefectTrigger trigger;
    
    @JsonProperty("template")
    private DefectTemplate template;
    
    @JsonProperty("options")
    private DefectOptions options;
    
    // 构造函数
    public DefectRule() {}
    
    public DefectRule(String id, String name, String description, boolean enabled) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.enabled = enabled;
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public DefectTrigger getTrigger() { return trigger; }
    public void setTrigger(DefectTrigger trigger) { this.trigger = trigger; }
    
    public DefectTemplate getTemplate() { return template; }
    public void setTemplate(DefectTemplate template) { this.template = template; }
    
    public DefectOptions getOptions() { return options; }
    public void setOptions(DefectOptions options) { this.options = options; }
    
    /**
     * 触发条件
     */
    public static class DefectTrigger {
        private List<String> status;
        private List<DefectCondition> conditions;
        
        public List<String> getStatus() { return status; }
        public void setStatus(List<String> status) { this.status = status; }
        
        public List<DefectCondition> getConditions() { return conditions; }
        public void setConditions(List<DefectCondition> conditions) { this.conditions = conditions; }
    }
    
    /**
     * 缺陷模板
     */
    public static class DefectTemplate {
        @JsonProperty("titleTemplate")
        private String titleTemplate;
        
        @JsonProperty("descriptionTemplate")
        private String descriptionTemplate;
        
        @JsonProperty("severityMapping")
        private Map<String, String> severityMapping;
        
        @JsonProperty("categoryMapping")
        private Map<String, String> categoryMapping;
        
        @JsonProperty("customFieldMapping")
        private Map<String, String> customFieldMapping;
        
        public String getTitleTemplate() { return titleTemplate; }
        public void setTitleTemplate(String titleTemplate) { this.titleTemplate = titleTemplate; }
        
        public String getDescriptionTemplate() { return descriptionTemplate; }
        public void setDescriptionTemplate(String descriptionTemplate) { this.descriptionTemplate = descriptionTemplate; }
        
        public Map<String, String> getSeverityMapping() { return severityMapping; }
        public void setSeverityMapping(Map<String, String> severityMapping) { this.severityMapping = severityMapping; }
        
        public Map<String, String> getCategoryMapping() { return categoryMapping; }
        public void setCategoryMapping(Map<String, String> categoryMapping) { this.categoryMapping = categoryMapping; }
        
        public Map<String, String> getCustomFieldMapping() { return customFieldMapping; }
        public void setCustomFieldMapping(Map<String, String> customFieldMapping) { this.customFieldMapping = customFieldMapping; }
    }
    
    /**
     * 生成选项
     */
    public static class DefectOptions {
        @JsonProperty("autoGenerate")
        private boolean autoGenerate;
        
        @JsonProperty("requireConfirmation")
        private boolean requireConfirmation;
        
        @JsonProperty("batchGenerate")
        private boolean batchGenerate;
        
        public boolean isAutoGenerate() { return autoGenerate; }
        public void setAutoGenerate(boolean autoGenerate) { this.autoGenerate = autoGenerate; }
        
        public boolean isRequireConfirmation() { return requireConfirmation; }
        public void setRequireConfirmation(boolean requireConfirmation) { this.requireConfirmation = requireConfirmation; }
        
        public boolean isBatchGenerate() { return batchGenerate; }
        public void setBatchGenerate(boolean batchGenerate) { this.batchGenerate = batchGenerate; }
    }
}

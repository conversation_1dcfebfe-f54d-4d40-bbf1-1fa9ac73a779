package com.fasnote.alm.checklist.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 多人评审配置
 */
public class MultiReviewConfig {
    
    @JsonProperty("enabled")
    private Boolean enabled;
    
    @JsonProperty("maxReviewers")
    private Integer maxReviewers;
    
    @JsonProperty("requireAllApproval")
    private Boolean requireAllApproval;
    
    @JsonProperty("defaultReviewers")
    private List<ReviewerConfig> defaultReviewers;
    
    @JsonProperty("assignmentStrategy")
    private String assignmentStrategy; // "manual", "auto-equal", "auto-category"
    
    @JsonProperty("allowSelfAssignment")
    private Boolean allowSelfAssignment;
    
    // 默认构造函数
    public MultiReviewConfig() {}
    
    // 全参构造函数
    public MultiReviewConfig(Boolean enabled, Integer maxReviewers, Boolean requireAllApproval,
                           List<ReviewerConfig> defaultReviewers, String assignmentStrategy,
                           Boolean allowSelfAssignment) {
        this.enabled = enabled;
        this.maxReviewers = maxReviewers;
        this.requireAllApproval = requireAllApproval;
        this.defaultReviewers = defaultReviewers;
        this.assignmentStrategy = assignmentStrategy;
        this.allowSelfAssignment = allowSelfAssignment;
    }
    
    // Getters and Setters
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public Integer getMaxReviewers() {
        return maxReviewers;
    }
    
    public void setMaxReviewers(Integer maxReviewers) {
        this.maxReviewers = maxReviewers;
    }
    
    public Boolean getRequireAllApproval() {
        return requireAllApproval;
    }
    
    public void setRequireAllApproval(Boolean requireAllApproval) {
        this.requireAllApproval = requireAllApproval;
    }
    
    public List<ReviewerConfig> getDefaultReviewers() {
        return defaultReviewers;
    }
    
    public void setDefaultReviewers(List<ReviewerConfig> defaultReviewers) {
        this.defaultReviewers = defaultReviewers;
    }
    
    public String getAssignmentStrategy() {
        return assignmentStrategy;
    }
    
    public void setAssignmentStrategy(String assignmentStrategy) {
        this.assignmentStrategy = assignmentStrategy;
    }
    
    public Boolean getAllowSelfAssignment() {
        return allowSelfAssignment;
    }
    
    public void setAllowSelfAssignment(Boolean allowSelfAssignment) {
        this.allowSelfAssignment = allowSelfAssignment;
    }
    
    @Override
    public String toString() {
        return "MultiReviewConfig{" +
                "enabled=" + enabled +
                ", maxReviewers=" + maxReviewers +
                ", requireAllApproval=" + requireAllApproval +
                ", defaultReviewers=" + defaultReviewers +
                ", assignmentStrategy='" + assignmentStrategy + '\'' +
                ", allowSelfAssignment=" + allowSelfAssignment +
                '}';
    }
}

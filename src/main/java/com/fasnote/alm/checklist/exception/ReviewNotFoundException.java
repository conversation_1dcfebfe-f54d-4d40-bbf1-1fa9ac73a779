package com.fasnote.alm.checklist.exception;

/**
 * 评审实例未找到异常
 * 当请求的评审实例不存在时抛出
 */
public class ReviewNotFoundException extends ChecklistException {
    
    public ReviewNotFoundException(String reviewId) {
        super("REVIEW_NOT_FOUND", "评审实例不存在: " + reviewId);
    }
    
    public ReviewNotFoundException(String reviewId, Throwable cause) {
        super("REVIEW_NOT_FOUND", "评审实例不存在: " + reviewId, cause);
    }
}
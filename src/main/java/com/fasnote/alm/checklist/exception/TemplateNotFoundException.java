package com.fasnote.alm.checklist.exception;

/**
 * 模板未找到异常
 * 当请求的模板不存在时抛出
 */
public class TemplateNotFoundException extends ChecklistException {
    
    public TemplateNotFoundException(String templateId) {
        super("TEMPLATE_NOT_FOUND", "模板不存在: " + templateId);
    }
    
    public TemplateNotFoundException(String templateId, Throwable cause) {
        super("TEMPLATE_NOT_FOUND", "模板不存在: " + templateId, cause);
    }
}
package com.fasnote.alm.checklist.exception;

/**
 * 检查单系统基础异常类
 * 所有业务异常的基类
 */
public class ChecklistException extends RuntimeException {
    
    private final String errorCode;
    
    public ChecklistException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public ChecklistException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
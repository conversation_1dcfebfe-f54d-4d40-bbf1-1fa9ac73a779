package com.fasnote.alm.checklist.dto;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * API响应构建器工具类
 * 提供便捷的静态方法用于创建统一的API响应
 */
public class ResponseBuilder {
    
    /**
     * 创建成功响应（无数据）
     * 
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> success() {
        return success(null, null);
    }
    
    /**
     * 创建成功响应（带数据）
     * 
     * @param <T> 数据类型
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> success(T data) {
        return success(data, null);
    }
    
    /**
     * 创建成功响应（带数据和消息）
     * 
     * @param <T> 数据类型
     * @param data 响应数据
     * @param message 响应消息
     * @return 成功响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> success(T data, String message) {
        ApiResponse<T> response = new ApiResponse<>(true, data, message);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建成功响应（带数据、消息和HTTP状态码）
     * 
     * @param <T> 数据类型
     * @param data 响应数据
     * @param message 响应消息
     * @param status HTTP状态码
     * @return 成功响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> success(T data, String message, HttpStatus status) {
        ApiResponse<T> response = new ApiResponse<>(true, data, message);
        return ResponseEntity.status(status).body(response);
    }
    
    /**
     * 创建失败响应（仅消息）
     * 
     * @param <T> 数据类型
     * @param message 错误消息
     * @return 失败响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> error(String message) {
        return error(null, message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 创建失败响应（带错误代码和消息）
     * 
     * @param <T> 数据类型
     * @param errorCode 错误代码
     * @param message 错误消息
     * @return 失败响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> error(String errorCode, String message) {
        return error(errorCode, message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 创建失败响应（带错误代码、消息和HTTP状态码）
     * 
     * @param <T> 数据类型
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param status HTTP状态码
     * @return 失败响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> error(String errorCode, String message, HttpStatus status) {
        ApiResponse<T> response = new ApiResponse<>(false, null, message, errorCode);
        return ResponseEntity.status(status).body(response);
    }
    
    /**
     * 创建参数验证失败响应
     * 
     * @param <T> 数据类型
     * @param message 错误消息
     * @return 参数验证失败响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> badRequest(String message) {
        return error("INVALID_PARAMETER", message, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 创建资源未找到响应
     * 
     * @param <T> 数据类型
     * @param message 错误消息
     * @return 资源未找到响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> notFound(String message) {
        return error("RESOURCE_NOT_FOUND", message, HttpStatus.NOT_FOUND);
    }
    
    /**
     * 创建验证错误响应
     * 
     * @param <T> 数据类型
     * @param message 错误消息
     * @return 验证错误响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> validationError(String message) {
        return error("VALIDATION_ERROR", message, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 创建内部服务器错误响应
     * 
     * @param <T> 数据类型
     * @param message 错误消息
     * @return 内部服务器错误响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> internalError(String message) {
        return error("INTERNAL_SERVER_ERROR", message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 创建创建成功响应（HTTP 201）
     * 
     * @param <T> 数据类型
     * @param data 响应数据
     * @return 创建成功响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> created(T data) {
        return success(data, null, HttpStatus.CREATED);
    }
    
    /**
     * 创建创建成功响应（HTTP 201，带消息）
     * 
     * @param <T> 数据类型
     * @param data 响应数据
     * @param message 响应消息
     * @return 创建成功响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> created(T data, String message) {
        return success(data, message, HttpStatus.CREATED);
    }
}

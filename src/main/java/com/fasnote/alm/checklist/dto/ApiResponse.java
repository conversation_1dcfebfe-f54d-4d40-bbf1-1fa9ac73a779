package com.fasnote.alm.checklist.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;

/**
 * 统一API响应体
 * 
 * @param <T> 响应数据类型
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    /**
     * 响应是否成功
     */
    @JsonProperty("success")
    private boolean success;
    
    /**
     * 响应数据
     */
    @JsonProperty("data")
    private T data;
    
    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;
    
    /**
     * 错误代码（仅在失败时使用）
     */
    @JsonProperty("errorCode")
    private String errorCode;
    
    /**
     * 响应时间戳
     */
    @JsonProperty("timestamp")
    private Long timestamp;
    
    /**
     * 请求路径（可选）
     */
    @JsonProperty("path")
    private String path;
    
    /**
     * 默认构造函数
     */
    public ApiResponse() {
        this.timestamp = Instant.now().toEpochMilli();
    }
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param data 响应数据
     * @param message 响应消息
     * @param errorCode 错误代码
     */
    public ApiResponse(boolean success, T data, String message, String errorCode) {
        this();
        this.success = success;
        this.data = data;
        this.message = message;
        this.errorCode = errorCode;
    }
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param data 响应数据
     * @param message 响应消息
     */
    public ApiResponse(boolean success, T data, String message) {
        this(success, data, message, null);
    }
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param data 响应数据
     */
    public ApiResponse(boolean success, T data) {
        this(success, data, null, null);
    }
    
    // Getter和Setter方法
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    @Override
    public String toString() {
        return "ApiResponse{" +
                "success=" + success +
                ", data=" + data +
                ", message='" + message + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", timestamp=" + timestamp +
                ", path='" + path + '\'' +
                '}';
    }
}

package com.fasnote.alm.checklist.service.impl;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasnote.alm.checklist.service.IRepositoryFileService;
import com.polarion.core.util.RunnableWEx;
import com.polarion.platform.TransactionExecuter;
import com.polarion.platform.service.repository.IRepositoryConnection;
import com.polarion.platform.service.repository.IRepositoryReadOnlyConnection;
import com.polarion.platform.service.repository.IRepositoryService;
import com.polarion.subterra.base.location.ILocation;


/**
 * SVN仓库文件操作服务实现类
 */
@Service
public class RepositoryFileServiceImpl implements IRepositoryFileService {
	private final IRepositoryService repositoryService;
	private static final Logger log = LoggerFactory.getLogger(RepositoryFileServiceImpl.class);
	public RepositoryFileServiceImpl(IRepositoryService repositoryService) {
		this.repositoryService = repositoryService;
	}

	@Override
	public InputStream readFile(ILocation location) {
		try {
			IRepositoryReadOnlyConnection conn = repositoryService.getReadOnlyConnection(location);
			if (!conn.exists(location)) {
				return null;
			}
			return conn.getContent(location);
		} catch (Exception e) {
			log.error("Failed to read file at location: {}", location, e);
			return null;
		}
	}

	@Override
	public boolean writeFile(ILocation location, String content) {
		return writeFile(location, new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8)));
	}

	private <T> T write(Supplier<T> supplier) {
		return TransactionExecuter.executeSafely(new RunnableWEx<T>() {
			@Override
			public T runWEx() throws Exception {
				return supplier.get();
			}
		});
	}

	@Override
	public boolean writeFile(ILocation location, InputStream inputStream) {
		try {
			return write(() -> {
				IRepositoryConnection conn = repositoryService.getConnection(location);
				conn.setContent(location, inputStream);
				return true;
			});
		} catch (Exception e) {
			log.error("Failed to write file at location: {}", location, e);
			return false;
		}
	}

	@Override
	public boolean deleteFile(ILocation location) {
		try {
			return write(() -> {
				IRepositoryConnection conn = repositoryService.getConnection(location);
				if (conn.exists(location)) {
					conn.delete(location);
					return true;
				}
				return false;
			});
		} catch (Exception e) {
			log.error("Failed to delete file at location: {}", location, e);
			return false;
		}
	}

	@Override
	public boolean exists(ILocation location) {
		try {
			IRepositoryReadOnlyConnection conn = repositoryService.getReadOnlyConnection(location);
			return conn.exists(location);
		} catch (Exception e) {
			log.error("Failed to check if file exists at location: {}", location, e);
			return false;
		}
	}

	@Override
	public List<String> listFiles(ILocation location) {
		List<String> fileNames = new ArrayList<>();
		try {
			IRepositoryReadOnlyConnection conn = repositoryService.getReadOnlyConnection(location);
			if (!conn.exists(location)) {
				return fileNames;
			}

			// 获取目录下的所有文件（不包含子目录）
			// 第二个参数为false表示不递归查找子目录中的文件
			// 第三个参数为false表示只获取文件，不获取文件夹
			List<ILocation> fileLocations = conn.getSubLocations(location, false, false);
			for (ILocation child : fileLocations) {
				// 使用getLastComponent获取文件名
				String name = child.getLastComponent();
				if (name != null) {
					fileNames.add(name);
				}
			}
		} catch (Exception e) {
			log.error("Failed to list files at location: {}", location, e);
		}
		return fileNames;
	}
}
package com.fasnote.alm.checklist.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasnote.alm.checklist.model.ChecklistTemplate;
import com.fasnote.alm.checklist.model.ReviewTemplateVersion;
import com.fasnote.alm.checklist.model.TemplateSnapshot;
import com.fasnote.alm.checklist.model.VersionComparison;
import com.fasnote.alm.checklist.repository.TemplateVersionRepository;
// import com.fasnote.alm.checklist.util.JsonFileUtil; // 不再直接使用
import com.fasnote.alm.checklist.util.VersionUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 模板版本管理服务
 */
@Service
public class TemplateVersionService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private ChecklistTemplateService templateService;
    
    // JsonFileUtil 已不再直接使用，通过Repository进行存储
    // @Autowired
    // private JsonFileUtil jsonFileUtil;

    @Autowired
    private TemplateVersionRepository templateVersionRepository;
    
    // 这些常量已不再使用，因为现在通过Repository进行存储
    // private static final String SNAPSHOTS_DIR = "data/template-snapshots";
    // private static final String REVIEW_VERSIONS_DIR = "data/review-template-versions";
    // private static final String VERSION_CONFIGS_DIR = "data/template-version-configs";
    
    /**
     * 创建模板快照
     */
    public TemplateSnapshot createSnapshot(String projectId, String templateId, String version, String description) throws Exception {
        // 获取模板信息
        Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(projectId, templateId);
        if (!templateOpt.isPresent()) {
            throw new RuntimeException("模板不存在");
        }
        
        ChecklistTemplate template = templateOpt.get();
        
        // 转换为JsonNode
        JsonNode templateData = objectMapper.valueToTree(template);
        JsonNode itemsData = objectMapper.valueToTree(template.getItems());
        
        // 获取配置数据（这里需要实现配置获取逻辑）
        JsonNode configData = getTemplateConfig(templateId);
        
        // 创建快照
        TemplateSnapshot snapshot = new TemplateSnapshot(templateId, version, "system", templateData, itemsData, configData);
        snapshot.setDescription(description);
        
        // 保存快照
        saveSnapshot(projectId, snapshot);

        return snapshot;
    }
    
    /**
     * 获取模板的所有快照
     */
    public Map<String, Object> getTemplateSnapshots(String projectId, String templateId, int limit, int offset, boolean includeContent) throws Exception {
        // 使用Repository获取快照
        List<TemplateSnapshot> snapshots = templateVersionRepository.findSnapshotsByTemplateId(projectId, templateId, limit, offset);

        // 如果不包含内容，清空模板内容以减少数据传输
        if (!includeContent) {
            snapshots.forEach(snapshot -> {
                snapshot.setTemplateData(null);
                snapshot.setItemsData(null);
                snapshot.setConfigData(null);
            });
        }

        // 获取总数
        long total = templateVersionRepository.countSnapshots(projectId, templateId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("snapshots", snapshots);
        result.put("total", total);
        result.put("hasMore", offset + limit < total);
        
        return result;
    }
    
    /**
     * 获取特定快照详情
     */
    public TemplateSnapshot getSnapshot(String projectId, String snapshotId) throws Exception {
        return loadSnapshot(projectId, snapshotId);
    }
    
    /**
     * 删除快照
     */
    public boolean deleteSnapshot(String projectId, String snapshotId) throws Exception {
        return templateVersionRepository.deleteSnapshot(projectId, snapshotId);
    }
    
    /**
     * 为检查单创建模板版本关联
     */
    public ReviewTemplateVersion createReviewTemplateVersion(String projectId, String reviewId, String templateId, String snapshotId) throws Exception {
        TemplateSnapshot snapshot;

        if (snapshotId == null || snapshotId.trim().isEmpty()) {
            // 如果没有指定快照ID，创建新快照
            String version = generateNextVersion(projectId, templateId);
            snapshot = createSnapshot(projectId, templateId, version, "检查单 " + reviewId + " 创建时的模板快照");
            snapshotId = snapshot.getId();
        } else {
            // 验证快照是否存在
            snapshot = getSnapshot(projectId, snapshotId);
            if (snapshot == null) {
                throw new RuntimeException("快照不存在");
            }
        }
        
        // 创建关联关系
        ReviewTemplateVersion reviewVersion = new ReviewTemplateVersion(reviewId, templateId, snapshotId, snapshot.getVersion());
        reviewVersion.setSnapshot(snapshot);

        // 保存版本关联信息
        saveReviewTemplateVersion(projectId, reviewVersion);

        return reviewVersion;
    }
    
    /**
     * 获取检查单的模板版本信息
     */
    public ReviewTemplateVersion getReviewTemplateVersion(String projectId, String reviewId) throws Exception {
        ReviewTemplateVersion reviewVersion = loadReviewTemplateVersion(projectId, reviewId);

        if (reviewVersion != null && reviewVersion.getSnapshot() == null) {
            // 如果快照信息为空，尝试加载
            try {
                TemplateSnapshot snapshot = getSnapshot(projectId, reviewVersion.getSnapshotId());
                reviewVersion.setSnapshot(snapshot);
            } catch (Exception e) {
                // 忽略快照加载失败
            }
        }

        return reviewVersion;
    }
    
    /**
     * 比较两个版本
     */
    public VersionComparison compareVersions(String projectId, String currentSnapshotId, String targetSnapshotId) throws Exception {
        TemplateSnapshot currentSnapshot = getSnapshot(projectId, currentSnapshotId);
        TemplateSnapshot targetSnapshot = getSnapshot(projectId, targetSnapshotId);
        
        if (currentSnapshot == null || targetSnapshot == null) {
            throw new RuntimeException("快照不存在");
        }
        
        return VersionUtil.compareSnapshots(currentSnapshot, targetSnapshot);
    }
    
    /**
     * 检查检查单是否需要版本升级
     */
    public Map<String, Object> checkUpgradeAvailable(String projectId, String reviewId) throws Exception {
        ReviewTemplateVersion reviewVersion = getReviewTemplateVersion(projectId, reviewId);
        if (reviewVersion == null) {
            throw new RuntimeException("检查单模板版本信息不存在");
        }
        
        // 获取模板的最新快照
        Map<String, Object> snapshotsResult = getTemplateSnapshots(projectId, reviewVersion.getTemplateId(), 1, 0, false);
        @SuppressWarnings("unchecked")
        List<TemplateSnapshot> snapshots = (List<TemplateSnapshot>) snapshotsResult.get("snapshots");
        
        Map<String, Object> result = new HashMap<>();
        
        if (snapshots.isEmpty()) {
            result.put("upgradeAvailable", false);
            result.put("currentVersion", reviewVersion.getVersion());
            result.put("latestVersion", reviewVersion.getVersion());
            result.put("latestSnapshotId", reviewVersion.getSnapshotId());
        } else {
            TemplateSnapshot latestSnapshot = snapshots.get(0);
            boolean upgradeAvailable = !latestSnapshot.getId().equals(reviewVersion.getSnapshotId());
            
            result.put("upgradeAvailable", upgradeAvailable);
            result.put("currentVersion", reviewVersion.getVersion());
            result.put("latestVersion", latestSnapshot.getVersion());
            result.put("latestSnapshotId", latestSnapshot.getId());
            
            // 如果有升级，提供变更信息
            if (upgradeAvailable) {
                VersionComparison comparison = compareVersions(projectId, reviewVersion.getSnapshotId(), latestSnapshot.getId());
                result.put("changes", comparison);
            }
        }
        
        return result;
    }
    
    /**
     * 升级检查单到新版本
     */
    public Map<String, Object> upgradeReviewVersion(String projectId, String reviewId, Map<String, Object> options) throws Exception {
        String targetSnapshotId = (String) options.get("targetSnapshotId");
        if (targetSnapshotId == null || targetSnapshotId.trim().isEmpty()) {
            throw new RuntimeException("目标快照ID不能为空");
        }
        
        ReviewTemplateVersion reviewVersion = getReviewTemplateVersion(projectId, reviewId);
        if (reviewVersion == null) {
            throw new RuntimeException("检查单版本信息不存在");
        }
        
        TemplateSnapshot targetSnapshot = getSnapshot(projectId, targetSnapshotId);
        if (targetSnapshot == null) {
            throw new RuntimeException("目标快照不存在");
        }
        
        // 更新版本关联
        reviewVersion.setSnapshotId(targetSnapshotId);
        reviewVersion.setVersion(targetSnapshot.getVersion());
        reviewVersion.setSnapshot(targetSnapshot);
        
        // 保存更新后的版本信息
        templateVersionRepository.saveReviewTemplateVersion(projectId, reviewVersion);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("reviewId", reviewId);
        result.put("oldSnapshotId", reviewVersion.getSnapshotId());
        result.put("newSnapshotId", targetSnapshotId);
        
        Map<String, Object> changes = new HashMap<>();
        changes.put("itemsAdded", 0);
        changes.put("itemsRemoved", 0);
        changes.put("itemsModified", 0);
        changes.put("configsUpdated", 0);
        result.put("changes", changes);
        
        result.put("conflicts", new ArrayList<>());
        result.put("errors", new ArrayList<>());
        result.put("warnings", new ArrayList<>());
        
        return result;
    }
    
    /**
     * 回滚检查单到指定版本
     */
    public Map<String, Object> rollbackReviewVersion(String projectId, String reviewId, String targetSnapshotId, Map<String, Object> options) throws Exception {
        // 回滚逻辑与升级类似，但是方向相反
        Map<String, Object> upgradeOptions = new HashMap<>();
        upgradeOptions.put("targetSnapshotId", targetSnapshotId);
        
        return upgradeReviewVersion(projectId, reviewId, upgradeOptions);
    }
    
    /**
     * 获取检查单的版本历史
     */
    public List<Map<String, Object>> getReviewVersionHistory(String projectId, String reviewId) throws Exception {
        List<Map<String, Object>> history = new ArrayList<>();
        
        ReviewTemplateVersion reviewVersion = getReviewTemplateVersion(projectId, reviewId);
        if (reviewVersion != null) {
            Map<String, Object> historyItem = new HashMap<>();
            historyItem.put("snapshotId", reviewVersion.getSnapshotId());
            historyItem.put("version", reviewVersion.getVersion());
            historyItem.put("createdTime", reviewVersion.getCreatedTime());
            historyItem.put("changeType", "create");
            history.add(historyItem);
        }
        
        return history;
    }
    
    /**
     * 获取模板版本管理配置
     */
    public Map<String, Object> getVersionConfig(String templateId) throws Exception {
        // 暂时返回默认配置，VERSION_CONFIGS_DIR已被注释
        Map<String, Object> config = null;
        
        if (config == null) {
            // 返回默认配置
            config = createDefaultVersionConfig();
        }
        
        return config;
    }
    
    /**
     * 更新模板版本管理配置
     */
    public void updateVersionConfig(String templateId, Map<String, Object> versionConfig) throws Exception {
        // 暂时不实现，VERSION_CONFIGS_DIR已被注释
        throw new UnsupportedOperationException("版本配置功能需要重构为支持项目ID");
    }
    
    /**
     * 自动创建快照（基于配置规则）
     */
    public TemplateSnapshot autoCreateSnapshot(String projectId, String templateId, String changeType) throws Exception {
        Map<String, Object> config = getVersionConfig(templateId);
        @SuppressWarnings("unchecked")
        Map<String, Object> autoSnapshot = (Map<String, Object>) config.get("autoSnapshot");
        
        if (autoSnapshot == null || !Boolean.TRUE.equals(autoSnapshot.get("enabled"))) {
            return null;
        }
        
        // 检查是否需要创建快照
        boolean shouldCreate = false;
        switch (changeType) {
            case "template":
                shouldCreate = Boolean.TRUE.equals(autoSnapshot.get("onTemplateChange"));
                break;
            case "config":
                shouldCreate = Boolean.TRUE.equals(autoSnapshot.get("onConfigChange"));
                break;
            case "publish":
                shouldCreate = Boolean.TRUE.equals(autoSnapshot.get("onPublish"));
                break;
        }
        
        if (!shouldCreate) {
            return null;
        }
        
        // 生成版本号
        String version = generateNextVersion(projectId, templateId);

        // 创建快照
        return createSnapshot(projectId, templateId, version, "自动快照 - " + changeType + " 变更");
    }
    
    // 私有辅助方法
    
    private String generateNextVersion(String projectId, String templateId) throws Exception {
        Map<String, Object> snapshotsResult = getTemplateSnapshots(projectId, templateId, 1, 0, false);
        @SuppressWarnings("unchecked")
        List<TemplateSnapshot> snapshots = (List<TemplateSnapshot>) snapshotsResult.get("snapshots");
        
        String currentVersion = snapshots.isEmpty() ? null : snapshots.get(0).getVersion();
        return VersionUtil.generateNextVersion(currentVersion, "minor");
    }
    
    private JsonNode getTemplateConfig(String templateId) throws Exception {
        // 这里应该获取模板的配置信息
        // 目前返回空配置
        Map<String, Object> config = new HashMap<>();
        config.put("buttonGroups", new ArrayList<>());
        config.put("defectRules", new ArrayList<>());
        config.put("completionButtons", null);
        return objectMapper.valueToTree(config);
    }
    
    private Map<String, Object> createDefaultVersionConfig() {
        Map<String, Object> config = new HashMap<>();
        
        Map<String, Object> autoSnapshot = new HashMap<>();
        autoSnapshot.put("enabled", true);
        autoSnapshot.put("onTemplateChange", true);
        autoSnapshot.put("onConfigChange", true);
        autoSnapshot.put("onPublish", true);
        autoSnapshot.put("minInterval", 60);
        config.put("autoSnapshot", autoSnapshot);
        
        Map<String, Object> retention = new HashMap<>();
        retention.put("maxVersions", 50);
        retention.put("maxAge", 365);
        retention.put("keepMajorVersions", true);
        config.put("retention", retention);
        
        Map<String, Object> versioning = new HashMap<>();
        versioning.put("scheme", "semantic");
        versioning.put("autoIncrement", true);
        config.put("versioning", versioning);
        
        return config;
    }

    /**
     * 保存快照
     */
    private void saveSnapshot(String projectId, TemplateSnapshot snapshot) throws Exception {
        try {
            templateVersionRepository.saveSnapshot(projectId, snapshot);
        } catch (IOException e) {
            throw new Exception("保存快照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载快照
     */
    private TemplateSnapshot loadSnapshot(String projectId, String snapshotId) throws Exception {
        try {
            Optional<TemplateSnapshot> snapshotOpt = templateVersionRepository.findSnapshotById(projectId, snapshotId);
            return snapshotOpt.orElse(null);
        } catch (IOException e) {
            throw new Exception("加载快照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存检查单版本关联
     */
    private void saveReviewTemplateVersion(String projectId, ReviewTemplateVersion reviewVersion) throws Exception {
        try {
            templateVersionRepository.saveReviewTemplateVersion(projectId, reviewVersion);
        } catch (IOException e) {
            throw new Exception("保存版本关联失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载检查单版本关联
     */
    private ReviewTemplateVersion loadReviewTemplateVersion(String projectId, String reviewId) throws Exception {
        try {
            Optional<ReviewTemplateVersion> reviewVersionOpt = templateVersionRepository.findReviewTemplateVersionByReviewId(projectId, reviewId);
            return reviewVersionOpt.orElse(null);
        } catch (IOException e) {
            throw new Exception("加载版本关联失败: " + e.getMessage(), e);
        }
    }




}

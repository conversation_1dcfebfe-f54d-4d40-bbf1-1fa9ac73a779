package com.fasnote.alm.checklist.service;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.checklist.util.FileUtil;
import com.fasnote.alm.checklist.util.JsonUtil;

/**
 * 文件存储管理服务
 * 提供数据备份、恢复和文件完整性检查功能
 */
public class FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageService.class);

    private static final String BACKUP_DIR = "data/backup";
    private static final String CONFIG_DIR = "data/config";
    private static final String CHECKSUM_SUFFIX = ".checksum";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    // 文件校验和缓存
    private final Map<String, String> checksumCache = new ConcurrentHashMap<>();
    
    /**
     * 备份指定文件
     * 
     * @param sourceFilePath 源文件路径
     * @return 备份文件路径
     * @throws IOException IO异常
     */
    public String backupFile(String sourceFilePath) throws IOException {
        if (sourceFilePath == null || sourceFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("源文件路径不能为空");
        }
        
        if (!FileUtil.fileExists(sourceFilePath)) {
            throw new IllegalArgumentException("源文件不存在: " + sourceFilePath);
        }
        
        // 生成备份文件路径
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        Path sourcePath = Paths.get(sourceFilePath);
        String fileName = sourcePath.getFileName().toString();
        String backupFileName = fileName + ".backup_" + timestamp;
        String backupFilePath = Paths.get(BACKUP_DIR, getRelativeBackupPath(sourceFilePath), backupFileName).toString();
        
        // 确保备份目录存在
        FileUtil.createDirectories(Paths.get(backupFilePath).getParent().toString());
        
        // 复制文件到备份位置
        Files.copy(sourcePath, Paths.get(backupFilePath), StandardCopyOption.REPLACE_EXISTING);
        
        // 生成并保存校验和
        String checksum = calculateFileChecksum(sourceFilePath);
        saveChecksum(backupFilePath, checksum);
        
        return backupFilePath;
    }
    
    /**
     * 批量备份文件
     * 
     * @param sourceFilePaths 源文件路径列表
     * @return 备份文件路径映射（源文件路径 -> 备份文件路径）
     * @throws IOException IO异常
     */
    public Map<String, String> backupFiles(List<String> sourceFilePaths) throws IOException {
        if (sourceFilePaths == null || sourceFilePaths.isEmpty()) {
            throw new IllegalArgumentException("源文件路径列表不能为空");
        }
        
        Map<String, String> backupMap = new HashMap<>();
        List<String> failedFiles = new ArrayList<>();
        
        for (String sourceFilePath : sourceFilePaths) {
            try {
                String backupFilePath = backupFile(sourceFilePath);
                backupMap.put(sourceFilePath, backupFilePath);
            } catch (Exception e) {
                failedFiles.add(sourceFilePath + ": " + e.getMessage());
            }
        }
        
        if (!failedFiles.isEmpty()) {
            throw new IOException("部分文件备份失败: " + String.join(", ", failedFiles));
        }
        
        return backupMap;
    }
    
    /**
     * 恢复文件从备份
     * 
     * @param backupFilePath 备份文件路径
     * @param targetFilePath 目标文件路径
     * @throws IOException IO异常
     */
    public void restoreFile(String backupFilePath, String targetFilePath) throws IOException {
        if (backupFilePath == null || backupFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("备份文件路径不能为空");
        }
        if (targetFilePath == null || targetFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("目标文件路径不能为空");
        }
        
        if (!FileUtil.fileExists(backupFilePath)) {
            throw new IllegalArgumentException("备份文件不存在: " + backupFilePath);
        }
        
        // 验证备份文件完整性
        if (!verifyFileIntegrity(backupFilePath)) {
            throw new IOException("备份文件完整性验证失败: " + backupFilePath);
        }
        
        // 确保目标目录存在
        FileUtil.createDirectories(Paths.get(targetFilePath).getParent().toString());
        
        // 恢复文件
        Files.copy(Paths.get(backupFilePath), Paths.get(targetFilePath), StandardCopyOption.REPLACE_EXISTING);
    }
    
    /**
     * 获取文件的所有备份
     * 
     * @param sourceFilePath 源文件路径
     * @return 备份文件信息列表
     * @throws IOException IO异常
     */
    public List<BackupInfo> getFileBackups(String sourceFilePath) throws IOException {
        if (sourceFilePath == null || sourceFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("源文件路径不能为空");
        }
        
        List<BackupInfo> backups = new ArrayList<>();
        String backupDirPath = Paths.get(BACKUP_DIR, getRelativeBackupPath(sourceFilePath)).toString();
        
        if (!FileUtil.fileExists(backupDirPath)) {
            return backups;
        }
        
        Path backupDir = Paths.get(backupDirPath);
        String fileName = Paths.get(sourceFilePath).getFileName().toString();
        String backupPrefix = fileName + ".backup_";
        
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(backupDir, 
                path -> path.getFileName().toString().startsWith(backupPrefix))) {
            
            for (Path backupFile : stream) {
                BackupInfo info = new BackupInfo();
                info.setBackupFilePath(backupFile.toString());
                info.setSourceFilePath(sourceFilePath);
                info.setBackupTime(FileUtil.getLastModifiedTime(backupFile.toString()));
                info.setFileSize(FileUtil.getFileSize(backupFile.toString()));
                info.setIntegrityValid(verifyFileIntegrity(backupFile.toString()));
                
                backups.add(info);
            }
        }
        
        // 按备份时间倒序排序
        backups.sort((b1, b2) -> {
            if (b1.getBackupTime() == null && b2.getBackupTime() == null) {
                return 0;
            }
            if (b1.getBackupTime() == null) {
                return 1;
            }
            if (b2.getBackupTime() == null) {
                return -1;
            }
            return b2.getBackupTime().compareTo(b1.getBackupTime());
        });
        
        return backups;
    }
    
    /**
     * 清理过期的备份文件
     * 
     * @param sourceFilePath 源文件路径
     * @param retentionDays 保留天数
     * @return 清理的文件数量
     * @throws IOException IO异常
     */
    public int cleanupExpiredBackups(String sourceFilePath, int retentionDays) throws IOException {
        if (sourceFilePath == null || sourceFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("源文件路径不能为空");
        }
        if (retentionDays < 0) {
            throw new IllegalArgumentException("保留天数不能为负数");
        }
        
        List<BackupInfo> backups = getFileBackups(sourceFilePath);
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
        
        int cleanedCount = 0;
        for (BackupInfo backup : backups) {
            if (backup.getBackupTime() != null && backup.getBackupTime().isBefore(cutoffTime)) {
                try {
                    // 删除备份文件
                    FileUtil.deleteFile(backup.getBackupFilePath());
                    // 删除对应的校验和文件
                    String checksumFilePath = backup.getBackupFilePath() + CHECKSUM_SUFFIX;
                    if (FileUtil.fileExists(checksumFilePath)) {
                        FileUtil.deleteFile(checksumFilePath);
                    }
                    cleanedCount++;
                } catch (IOException e) {
                    // 记录错误但继续处理其他文件
                    logger.error("删除备份文件失败: {}, 错误: {}", backup.getBackupFilePath(), e.getMessage(), e);
                }
            }
        }
        
        return cleanedCount;
    }
    
    /**
     * 验证文件完整性
     * 
     * @param filePath 文件路径
     * @return 是否完整
     * @throws IOException IO异常
     */
    public boolean verifyFileIntegrity(String filePath) throws IOException {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }
        
        if (!FileUtil.fileExists(filePath)) {
            return false;
        }
        
        String checksumFilePath = filePath + CHECKSUM_SUFFIX;
        if (!FileUtil.fileExists(checksumFilePath)) {
            // 如果没有校验和文件，认为文件是完整的（向后兼容）
            return true;
        }
        
        try {
            // 读取保存的校验和
            String savedChecksum = FileUtil.readFile(checksumFilePath);
            if (savedChecksum == null || savedChecksum.trim().isEmpty()) {
                return false;
            }
            
            // 计算当前文件的校验和
            String currentChecksum = calculateFileChecksum(filePath);
            
            return savedChecksum.trim().equals(currentChecksum);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 批量验证文件完整性
     * 
     * @param filePaths 文件路径列表
     * @return 验证结果映射（文件路径 -> 是否完整）
     * @throws IOException IO异常
     */
    public Map<String, Boolean> verifyFilesIntegrity(List<String> filePaths) throws IOException {
        if (filePaths == null || filePaths.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<String, Boolean> results = new HashMap<>();
        
        for (String filePath : filePaths) {
            try {
                boolean isValid = verifyFileIntegrity(filePath);
                results.put(filePath, isValid);
            } catch (Exception e) {
                results.put(filePath, false);
            }
        }
        
        return results;
    }
    
    /**
     * 获取存储统计信息
     * 
     * @return 存储统计信息
     * @throws IOException IO异常
     */
    public StorageStatistics getStorageStatistics() throws IOException {
        StorageStatistics stats = new StorageStatistics();
        
        // 统计备份文件
        if (FileUtil.fileExists(BACKUP_DIR)) {
            Path backupPath = Paths.get(BACKUP_DIR);
            try (var stream = Files.walk(backupPath)) {
                List<Path> backupFiles = stream
                    .filter(Files::isRegularFile)
                    .filter(path -> path.getFileName().toString().contains(".backup_"))
                    .collect(Collectors.toList());
                
                stats.setTotalBackupFiles(backupFiles.size());
                
                long totalSize = 0;
                for (Path file : backupFiles) {
                    totalSize += Files.size(file);
                }
                stats.setTotalBackupSize(totalSize);
            }
        }
        
        // 统计数据文件
        String[] dataDirs = {"data/templates", "data/reviews"};
        int totalDataFiles = 0;
        long totalDataSize = 0;
        
        for (String dataDir : dataDirs) {
            if (FileUtil.fileExists(dataDir)) {
                Path dataPath = Paths.get(dataDir);
                try (var stream = Files.walk(dataPath)) {
                    List<Path> dataFiles = stream
                        .filter(Files::isRegularFile)
                        .filter(path -> path.getFileName().toString().endsWith(".json"))
                        .collect(Collectors.toList());
                    
                    totalDataFiles += dataFiles.size();
                    for (Path file : dataFiles) {
                        totalDataSize += Files.size(file);
                    }
                }
            }
        }
        
        stats.setTotalDataFiles(totalDataFiles);
        stats.setTotalDataSize(totalDataSize);
        stats.setLastUpdateTime(LocalDateTime.now());
        
        return stats;
    }
    
    /**
     * 保存配置信息
     * 
     * @param configName 配置名称
     * @param configData 配置数据
     * @throws IOException IO异常
     */
    public void saveConfig(String configName, Object configData) throws IOException {
        if (configName == null || configName.trim().isEmpty()) {
            throw new IllegalArgumentException("配置名称不能为空");
        }
        if (configData == null) {
            throw new IllegalArgumentException("配置数据不能为空");
        }
        
        String configFilePath = Paths.get(CONFIG_DIR, configName + ".json").toString();
        String jsonContent = JsonUtil.toJson(configData);
        
        // 先备份现有配置（如果存在）
        if (FileUtil.fileExists(configFilePath)) {
            backupFile(configFilePath);
        }
        
        // 保存新配置
        FileUtil.safeWriteFile(configFilePath, jsonContent);
        
        // 生成校验和
        String checksum = calculateFileChecksum(configFilePath);
        saveChecksum(configFilePath, checksum);
    }
    
    /**
     * 加载配置信息
     * 
     * @param configName 配置名称
     * @param configClass 配置类型
     * @param <T> 配置类型
     * @return 配置对象
     * @throws IOException IO异常
     */
    public <T> T loadConfig(String configName, Class<T> configClass) throws IOException {
        if (configName == null || configName.trim().isEmpty()) {
            throw new IllegalArgumentException("配置名称不能为空");
        }
        if (configClass == null) {
            throw new IllegalArgumentException("配置类型不能为空");
        }
        
        String configFilePath = Paths.get(CONFIG_DIR, configName + ".json").toString();
        
        if (!FileUtil.fileExists(configFilePath)) {
            return null;
        }
        
        // 验证文件完整性
        if (!verifyFileIntegrity(configFilePath)) {
            throw new IOException("配置文件完整性验证失败: " + configFilePath);
        }
        
        String jsonContent = FileUtil.readFileWithLock(configFilePath);
        if (jsonContent == null || jsonContent.trim().isEmpty()) {
            return null;
        }
        
        return JsonUtil.fromJson(jsonContent, configClass);
    }
    
    /**
     * 计算文件校验和
     * 
     * @param filePath 文件路径
     * @return 校验和
     * @throws IOException IO异常
     */
    private String calculateFileChecksum(String filePath) throws IOException {
        // 先检查缓存
        String cachedChecksum = checksumCache.get(filePath);
        if (cachedChecksum != null) {
            // 检查文件是否被修改
            LocalDateTime lastModified = FileUtil.getLastModifiedTime(filePath);
            String cacheKey = filePath + "_" + (lastModified != null ? lastModified.toString() : "");
            if (checksumCache.containsKey(cacheKey)) {
                return checksumCache.get(cacheKey);
            }
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] fileBytes = Files.readAllBytes(Paths.get(filePath));
            byte[] hashBytes = md.digest(fileBytes);
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            
            String checksum = sb.toString();
            
            // 更新缓存
            LocalDateTime lastModified = FileUtil.getLastModifiedTime(filePath);
            String cacheKey = filePath + "_" + (lastModified != null ? lastModified.toString() : "");
            checksumCache.put(cacheKey, checksum);
            
            return checksum;
        } catch (NoSuchAlgorithmException e) {
            throw new IOException("不支持的哈希算法", e);
        }
    }
    
    /**
     * 保存校验和到文件
     * 
     * @param filePath 原文件路径
     * @param checksum 校验和
     * @throws IOException IO异常
     */
    private void saveChecksum(String filePath, String checksum) throws IOException {
        String checksumFilePath = filePath + CHECKSUM_SUFFIX;
        FileUtil.writeFile(checksumFilePath, checksum);
    }
    
    /**
     * 获取相对备份路径
     * 
     * @param sourceFilePath 源文件路径
     * @return 相对备份路径
     */
    private String getRelativeBackupPath(String sourceFilePath) {
        Path sourcePath = Paths.get(sourceFilePath);
        
        // 如果是data目录下的文件，保持相对路径结构
        if (sourceFilePath.startsWith("data/")) {
            Path relativePath = Paths.get("data").relativize(sourcePath.getParent());
            return relativePath.toString();
        }
        
        // 其他文件放在misc目录下
        return "misc";
    }
    
    /**
     * 备份信息类
     */
    public static class BackupInfo {
        private String backupFilePath;
        private String sourceFilePath;
        private LocalDateTime backupTime;
        private long fileSize;
        private boolean integrityValid;
        
        // Getters and Setters
        public String getBackupFilePath() {
            return backupFilePath;
        }
        
        public void setBackupFilePath(String backupFilePath) {
            this.backupFilePath = backupFilePath;
        }
        
        public String getSourceFilePath() {
            return sourceFilePath;
        }
        
        public void setSourceFilePath(String sourceFilePath) {
            this.sourceFilePath = sourceFilePath;
        }
        
        public LocalDateTime getBackupTime() {
            return backupTime;
        }
        
        public void setBackupTime(LocalDateTime backupTime) {
            this.backupTime = backupTime;
        }
        
        public long getFileSize() {
            return fileSize;
        }
        
        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }
        
        public boolean isIntegrityValid() {
            return integrityValid;
        }
        
        public void setIntegrityValid(boolean integrityValid) {
            this.integrityValid = integrityValid;
        }
    }
    
    /**
     * 存储统计信息类
     */
    public static class StorageStatistics {
        private int totalBackupFiles;
        private long totalBackupSize;
        private int totalDataFiles;
        private long totalDataSize;
        private LocalDateTime lastUpdateTime;
        
        // Getters and Setters
        public int getTotalBackupFiles() {
            return totalBackupFiles;
        }
        
        public void setTotalBackupFiles(int totalBackupFiles) {
            this.totalBackupFiles = totalBackupFiles;
        }
        
        public long getTotalBackupSize() {
            return totalBackupSize;
        }
        
        public void setTotalBackupSize(long totalBackupSize) {
            this.totalBackupSize = totalBackupSize;
        }
        
        public int getTotalDataFiles() {
            return totalDataFiles;
        }
        
        public void setTotalDataFiles(int totalDataFiles) {
            this.totalDataFiles = totalDataFiles;
        }
        
        public long getTotalDataSize() {
            return totalDataSize;
        }
        
        public void setTotalDataSize(long totalDataSize) {
            this.totalDataSize = totalDataSize;
        }
        
        public LocalDateTime getLastUpdateTime() {
            return lastUpdateTime;
        }
        
        public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
            this.lastUpdateTime = lastUpdateTime;
        }
    }
}
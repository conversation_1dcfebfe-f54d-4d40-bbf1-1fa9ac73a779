package com.fasnote.alm.checklist.service;

import java.io.InputStream;
import java.util.List;

import com.polarion.subterra.base.location.ILocation;

/**
 * SVN仓库文件操作服务接口
 * 封装对Polarion SVN仓库中文件的读写操作
 */
public interface IRepositoryFileService {
    
    /**
     * 读取文件内容
     * 
     * @param location 文件位置
     * @return 文件内容的输入流，如果文件不存在则返回null
     */
    InputStream readFile(ILocation location);
    
    /**
     * 将内容写入文件
     * 
     * @param location 文件位置
     * @param content 文件内容
     * @return 是否写入成功
     */
    boolean writeFile(ILocation location, String content);
    
    /**
     * 将输入流写入文件
     * 
     * @param location 文件位置
     * @param inputStream 文件内容输入流
     * @return 是否写入成功
     */
    boolean writeFile(ILocation location, InputStream inputStream);
    
    /**
     * 删除文件
     * 
     * @param location 文件位置
     * @return 是否删除成功
     */
    boolean deleteFile(ILocation location);
    
    /**
     * 检查文件是否存在
     * 
     * @param location 文件位置
     * @return 文件是否存在
     */
    boolean exists(ILocation location);
    
    /**
     * 获取目录下所有文件名
     * 
     * @param location 目录位置
     * @return 文件名列表
     */
    List<String> listFiles(ILocation location);
} 
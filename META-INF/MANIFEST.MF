Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Checklist
Bundle-SymbolicName: com.fasnote.alm.checklist;singleton:=true
Bundle-Version: 1.0.0.qualifier
Bundle-Activator: com.fasnote.alm.checklist.Activator
Bundle-ClassPath: .,
 lib/jackson-datatype-jsr310-2.11.1.jar
Bundle-Vendor: FASNOTE
Require-Bundle: org.eclipse.core.runtime,
 org.springframework.spring-aop;bundle-version="5.2.10",
 org.springframework.spring-beans;bundle-version="5.2.10",
 org.springframework.spring-context;bundle-version="5.2.10",
 org.springframework.spring-core;bundle-version="5.2.10",
 org.springframework.spring-expression;bundle-version="5.2.10",
 org.springframework.spring-jdbc;bundle-version="5.2.10",
 org.springframework.spring-orm;bundle-version="5.2.10",
 org.springframework.spring-test;bundle-version="5.2.10",
 org.springframework.spring-tx;bundle-version="5.2.10",
 org.springframework.spring-web;bundle-version="5.2.10",
 org.springframework.spring-webmvc;bundle-version="5.2.10",
 slf4j.api;bundle-version="1.7.32",
 org.apache.poi;bundle-version="4.1.0",
 com.polarion.portal.tomcat;bundle-version="9.0.53",
 com.polarion.alm.tracker;bundle-version="3.22.1"
Bundle-RequiredExecutionEnvironment: JavaSE-11
Automatic-Module-Name: com.fasnote.alm.checklist
Bundle-ActivationPolicy: lazy

<template>
  <div class="review-history">
    <!-- Header with filters -->
    <div class="history-header">
      <h2>评审历史</h2>
      <div class="filters">
        <el-form :model="filters" inline>
          <el-form-item label="评审类型">
            <el-select v-model="filters.type" placeholder="选择类型" clearable>
              <el-option
                v-for="type in reviewTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="评审状态">
            <el-select v-model="filters.status" placeholder="选择状态" clearable>
              <el-option
                v-for="status in reviewStatuses"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="评审人员">
            <el-input
              v-model="filters.reviewer"
              placeholder="输入评审人员"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="searchHistory">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- History table -->
    <div class="history-content">
      <el-table
        v-loading="loading"
        :data="historyList"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="id"
          label="评审ID"
          width="120"
          sortable="custom"
        />
        
        <el-table-column
          prop="type"
          label="评审类型"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="status"
          label="状态"
          width="100"
          sortable="custom"
        >
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="reviewer"
          label="评审人员"
          width="120"
          sortable="custom"
        />
        
        <el-table-column
          prop="progress"
          label="进度"
          width="150"
        >
          <template #default="{ row }">
            <div class="progress-info">
              <el-progress
                :percentage="row.progress.percentage"
                :stroke-width="8"
                :show-text="false"
              />
              <span class="progress-text">
                {{ row.progress.completed }}/{{ row.progress.total }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="createdTime"
          label="创建时间"
          width="160"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.createdTime) }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="updatedTime"
          label="更新时间"
          width="160"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.updatedTime) }}
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'IN_PROGRESS'"
              type="success"
              size="small"
              @click="continueReview(row)"
            >
              继续评审
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="viewDetails(row)"
            >
              查看详情
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewHistory(row)"
            >
              查看历史
            </el-button>
            <el-dropdown @command="handleCommand($event, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="export">导出结果</el-dropdown-item>
                  <el-dropdown-item command="duplicate">复制评审</el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.status !== 'CANCELLED'"
                    command="cancel"
                    divided
                  >
                    取消评审
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- Review details dialog -->
    <el-dialog
      v-model="detailsDialogVisible"
      title="评审详情"
      width="80%"
      :before-close="handleCloseDetails"
    >
      <div v-if="selectedReview" class="review-details">
        <div class="details-header">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="评审ID">
              {{ selectedReview.id }}
            </el-descriptions-item>
            <el-descriptions-item label="评审类型">
              <el-tag :type="getTypeTagType(selectedReview.type)">
                {{ selectedReview.type }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(selectedReview.status)">
                {{ getStatusText(selectedReview.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="评审人员">
              {{ selectedReview.reviewer || '未指定' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(selectedReview.createdTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDateTime(selectedReview.updatedTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="details-progress">
          <h4>评审进度</h4>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总计" :value="selectedReview.progress.total" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已完成" :value="selectedReview.progress.completed" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="通过" :value="selectedReview.progress.passed" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="不通过" :value="selectedReview.progress.failed" />
            </el-col>
          </el-row>
        </div>
        
        <div class="details-items">
          <h4>检查项详情</h4>
          <el-table :data="selectedReview.reviewItems" stripe border>
            <el-table-column prop="sequence" label="序号" width="80" />
            <el-table-column prop="content" label="检查内容" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getItemStatusTagType(row.status)">
                  {{ getItemStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="comment" label="备注" />
            <el-table-column prop="reviewTime" label="评审时间" width="160">
              <template #default="{ row }">
                {{ row.reviewTime ? formatDateTime(row.reviewTime) : '-' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- Review history dialog -->
    <el-dialog
      v-model="historyDialogVisible"
      title="评审历史记录"
      width="70%"
      :before-close="handleCloseHistory"
    >
      <ReviewerInfo
        v-if="selectedReview"
        :review-id="selectedReview.id"
        :review-history="reviewHistoryData"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, ArrowDown } from '@element-plus/icons-vue'
import reviewApi, {
  type ChecklistReview,
  type ReviewListQuery,
  type ReviewHistory,
  ReviewStatus,
  ReviewItemStatus
} from '@/api/review'
import ReviewerInfo from './ReviewerInfo.vue'

// Component props
interface Props {
  reviewId?: string
}

const props = withDefaults(defineProps<Props>(), {
  reviewId: ''
})

const router = useRouter()

// Reactive data
const loading = ref(false)
const historyList = ref<ChecklistReview[]>([])
const reviewHistoryData = ref<ReviewHistory[]>([])
const selectedReview = ref<ChecklistReview | null>(null)
const detailsDialogVisible = ref(false)
const historyDialogVisible = ref(false)
const dateRange = ref<[string, string] | null>(null)

// Filters
const filters = reactive<ReviewListQuery>({
  page: 1,
  size: 20,
  type: '',
  status: undefined,
  reviewer: '',
  startDate: '',
  endDate: ''
})

// Pagination
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// Sort
const sortConfig = reactive({
  prop: 'updatedTime',
  order: 'descending'
})

// Review types and statuses for filters
const reviewTypes = ref([
  { label: '代码评审', value: 'code-review' },
  { label: '设计评审', value: 'design-review' },
  { label: '测试评审', value: 'test-review' },
  { label: '部署评审', value: 'deploy-review' }
])

const reviewStatuses = computed(() => [
  { label: '待开始', value: ReviewStatus.PENDING },
  { label: '进行中', value: ReviewStatus.IN_PROGRESS },
  { label: '已完成', value: ReviewStatus.COMPLETED },
  { label: '已取消', value: ReviewStatus.CANCELLED }
])

// Methods
const loadHistoryList = async () => {
  try {
    loading.value = true
    
    const query: ReviewListQuery = {
      page: pagination.page,
      size: pagination.size,
      ...filters
    }
    
    const response = await reviewApi.getReviewList(query)
    historyList.value = response.reviews
    pagination.total = response.total
    
  } catch (error) {
    console.error('Failed to load review history:', error)
    ElMessage.error('加载评审历史失败')
  } finally {
    loading.value = false
  }
}

const searchHistory = () => {
  pagination.page = 1
  loadHistoryList()
}

const resetFilters = () => {
  Object.assign(filters, {
    page: 1,
    size: 20,
    type: '',
    status: undefined,
    reviewer: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = null
  pagination.page = 1
  loadHistoryList()
}

const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    filters.startDate = dates[0]
    filters.endDate = dates[1]
  } else {
    filters.startDate = ''
    filters.endDate = ''
  }
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortConfig.prop = prop
  sortConfig.order = order
  loadHistoryList()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadHistoryList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadHistoryList()
}

const continueReview = (review: ChecklistReview) => {
  // 导航到已有的评审实例
  router.push(`/review/${review.type}/${review.id}`)
}

const viewDetails = (review: ChecklistReview) => {
  selectedReview.value = review
  detailsDialogVisible.value = true
}

const viewHistory = async (review: ChecklistReview) => {
  try {
    selectedReview.value = review
    reviewHistoryData.value = await reviewApi.getReviewHistory(review.id)
    historyDialogVisible.value = true
  } catch (error) {
    console.error('Failed to load review history:', error)
    ElMessage.error('加载评审历史失败')
  }
}

const handleCommand = async (command: string, review: ChecklistReview) => {
  switch (command) {
    case 'export':
      await exportReview(review)
      break
    case 'duplicate':
      await duplicateReview(review)
      break
    case 'cancel':
      await cancelReview(review)
      break
  }
}

const exportReview = async (review: ChecklistReview) => {
  try {
    const blob = await reviewApi.exportReview(review.id)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `review-${review.id}-${Date.now()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Failed to export review:', error)
    ElMessage.error('导出失败')
  }
}

const duplicateReview = async (review: ChecklistReview) => {
  try {
    await ElMessageBox.confirm('确定要复制这个评审吗？', '确认操作', {
      type: 'warning'
    })
    
    await reviewApi.createReview({
      templateId: review.templateId,
      type: review.type,
      reviewer: review.reviewer
    })
    
    ElMessage.success('评审复制成功')
    loadHistoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to duplicate review:', error)
      ElMessage.error('复制评审失败')
    }
  }
}

const cancelReview = async (review: ChecklistReview) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消评审', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入取消原因...'
    })
    
    await reviewApi.cancelReview(review.id, reason)
    ElMessage.success('评审已取消')
    loadHistoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to cancel review:', error)
      ElMessage.error('取消评审失败')
    }
  }
}

const handleCloseDetails = () => {
  detailsDialogVisible.value = false
  selectedReview.value = null
}

const handleCloseHistory = () => {
  historyDialogVisible.value = false
  selectedReview.value = null
  reviewHistoryData.value = []
}

// Utility methods
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'code-review': 'primary',
    'design-review': 'success',
    'test-review': 'warning',
    'deploy-review': 'danger'
  }
  return typeMap[type] || 'info'
}

const getStatusTagType = (status: ReviewStatus) => {
  const statusMap: Record<ReviewStatus, string> = {
    [ReviewStatus.PENDING]: 'info',
    [ReviewStatus.IN_PROGRESS]: 'warning',
    [ReviewStatus.COMPLETED]: 'success',
    [ReviewStatus.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: ReviewStatus) => {
  const statusMap: Record<ReviewStatus, string> = {
    [ReviewStatus.PENDING]: '待开始',
    [ReviewStatus.IN_PROGRESS]: '进行中',
    [ReviewStatus.COMPLETED]: '已完成',
    [ReviewStatus.CANCELLED]: '已取消'
  }
  return statusMap[status] || status
}

const getItemStatusTagType = (status: ReviewItemStatus) => {
  const statusMap: Record<ReviewItemStatus, string> = {
    [ReviewItemStatus.PENDING]: 'info',
    [ReviewItemStatus.PASS]: 'success',
    [ReviewItemStatus.FAIL]: 'danger',
    [ReviewItemStatus.SKIP]: 'warning'
  }
  return statusMap[status] || 'info'
}

const getItemStatusText = (status: ReviewItemStatus) => {
  const statusMap: Record<ReviewItemStatus, string> = {
    [ReviewItemStatus.PENDING]: '待处理',
    [ReviewItemStatus.PASS]: '通过',
    [ReviewItemStatus.FAIL]: '不通过',
    [ReviewItemStatus.SKIP]: '跳过'
  }
  return statusMap[status] || status
}

// Lifecycle
onMounted(() => {
  loadHistoryList()
})
</script>

<style scoped>
.review-history {
  padding: 20px;
}

.history-header {
  margin-bottom: 20px;
}

.history-header h2 {
  margin: 0 0 16px 0;
  color: #303133;
}

.filters {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.history-content {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

.pagination-wrapper {
  padding: 16px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.review-details {
  max-height: 70vh;
  overflow-y: auto;
}

.details-header {
  margin-bottom: 24px;
}

.details-progress {
  margin-bottom: 24px;
}

.details-progress h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.details-items h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
</style>
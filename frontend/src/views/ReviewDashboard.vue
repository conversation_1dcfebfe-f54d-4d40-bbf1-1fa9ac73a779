<template>
  <div class="review-dashboard">
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <h1>Checklist Review System - 评审系统</h1>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="$router.push('/admin/templates')">
            <el-icon><Setting /></el-icon>
            后台管理
          </el-button>
        </div>
      </el-header>

      <el-main class="main-content">
        <Breadcrumb />
        
        <div class="welcome-section">
          <h2>欢迎使用评审系统</h2>
          <p>请选择评审类型开始评审工作，或查看最近的评审记录</p>
        </div>

        <div class="dashboard-content">
          <div class="review-types-section">
            <h3>评审类型</h3>
            <el-row :gutter="20">
              <el-col :span="8" v-for="reviewType in reviewTypes" :key="reviewType.type">
                <el-card class="review-type-card" @click="startReview(reviewType.type)">
                  <div class="card-content">
                    <el-icon class="card-icon" :size="48">
                      <component :is="reviewType.icon" />
                    </el-icon>
                    <h4>{{ reviewType.name }}</h4>
                    <p>{{ reviewType.description }}</p>
                    <div class="card-actions">
                      <el-button type="primary" size="small" @click.stop="startReview(reviewType.type)">
                        开始评审
                      </el-button>
                      <el-button size="small" @click.stop="viewHistory(reviewType.type)">
                        查看历史
                      </el-button>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div class="recent-reviews-section">
            <div class="section-header">
              <h3>最近的评审</h3>
              <el-button size="small" @click="refreshRecentReviews">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            
            <el-table :data="recentReviews" style="width: 100%" v-loading="loading">
              <el-table-column prop="type" label="类型" width="120" />
              <el-table-column prop="createdTime" label="创建时间" width="180" />
              <el-table-column prop="reviewer" label="评审人" width="120" />
              <el-table-column prop="progress" label="进度" width="100">
                <template #default="scope">
                  <el-progress 
                    :percentage="scope.row.progress" 
                    :stroke-width="6"
                    :show-text="false"
                  />
                  <span class="progress-text">{{ scope.row.progress }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="continueReview(scope.row)">
                    继续评审
                  </el-button>
                  <el-button size="small" @click="viewReviewDetail(scope.row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div v-if="recentReviews.length === 0 && !loading" class="empty-state">
              <el-empty description="暂无评审记录" />
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Document, Setting, Refresh, Edit, Brush, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import Breadcrumb from '@/components/common/Breadcrumb.vue'

const router = useRouter()
const loading = ref(false)

const reviewTypes = ref([
  {
    type: 'code-review',
    name: '代码评审',
    description: '对代码质量进行评审检查',
    icon: 'Edit',
  },
  {
    type: 'design-review',
    name: '设计评审',
    description: '对系统设计进行评审检查',
    icon: 'Brush',
  },
  {
    type: 'security-review',
    name: '安全评审',
    description: '对安全性进行评审检查',
    icon: 'Lock',
  },
])

const recentReviews = ref([
  {
    id: '1',
    type: '代码评审',
    createdTime: '2025-01-01 10:00:00',
    reviewer: '张三',
    progress: 75,
    status: 'IN_PROGRESS',
  },
  {
    id: '2',
    type: '设计评审',
    createdTime: '2025-01-01 09:30:00',
    reviewer: '李四',
    progress: 100,
    status: 'COMPLETED',
  },
  {
    id: '3',
    type: '安全评审',
    createdTime: '2025-01-01 08:45:00',
    reviewer: '王五',
    progress: 30,
    status: 'IN_PROGRESS',
  },
])

const startReview = (type: string) => {
  // 生成一个基于时间戳和随机数的ID，确保唯一性
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 6)
  const reviewId = `${type}-${timestamp}-${random}`
  router.push(`/review/${type}/${reviewId}`)
}

const viewHistory = (type: string) => {
  router.push(`/review/${type}/history`)
}

const continueReview = (review: any) => {
  // 导航到已有的评审实例，使用评审ID
  const reviewType = review.type.toLowerCase().replace('评审', '-review')
  router.push(`/review/${reviewType}/${review.id}`)
}

const viewReviewDetail = (review: any) => {
  // This will be implemented in future tasks
  ElMessage.info(`查看评审详情: ${review.id}`)
}

const refreshRecentReviews = async () => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'success'
    case 'IN_PROGRESS':
      return 'warning'
    case 'PENDING':
      return 'info'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return '已完成'
    case 'IN_PROGRESS':
      return '进行中'
    case 'PENDING':
      return '待处理'
    default:
      return '未知'
  }
}

onMounted(() => {
  // Load recent reviews from API in future tasks
  document.title = '评审系统 - Checklist Review System'
})
</script>

<style scoped>
.review-dashboard {
  height: 100vh;
}

.header {
  background-color: #67c23a;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
}

.main-content {
  padding: 20px;
  background-color: #f5f5f5;
}

.welcome-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-section h2 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.welcome-section p {
  color: #606266;
  font-size: 16px;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.review-types-section,
.recent-reviews-section {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.review-types-section h3,
.recent-reviews-section h3 {
  color: #303133;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
}

.review-type-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 100%;
}

.review-type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  text-align: center;
  padding: 30px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-icon {
  color: #409eff;
  margin-bottom: 15px;
}

.card-content h4 {
  margin: 10px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.card-content p {
  color: #606266;
  margin-bottom: 20px;
  font-size: 14px;
  flex-grow: 1;
}

.card-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.empty-state {
  padding: 40px 0;
}
</style>

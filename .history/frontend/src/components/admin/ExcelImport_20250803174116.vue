<template>
  <div class="excel-import">
    <!-- Header -->
    <div class="import-header">
      <h3>Excel 导入</h3>
      <p class="header-description">
        支持从 Excel 文件批量导入检查单模板，提高模板创建效率
      </p>
    </div>

    <!-- Import Steps -->
    <el-steps :active="currentStep" align-center class="import-steps">
      <el-step title="选择文件" description="上传 Excel 文件" />
      <el-step title="数据预览" description="确认导入数据" />
      <el-step title="导入完成" description="查看导入结果" />
    </el-steps>

    <!-- Step 1: File Selection -->
    <el-card v-if="currentStep === 0" class="step-card" shadow="never">
      <template #header>
        <span class="card-title">选择 Excel 文件</span>
      </template>

      <!-- File format instructions -->
      <el-alert
        title="Excel 文件格式要求"
        type="info"
        :closable="false"
        class="format-alert"
      >
        <div class="format-instructions">
          <p><strong>支持的文件格式：</strong>.xlsx, .xls</p>
          <p><strong>文件大小限制：</strong>最大 10MB</p>
          <p><strong>表格结构要求：</strong></p>
          <ul>
            <li>第一行为标题行（可选）</li>
            <li>A列：模板名称</li>
            <li>B列：模板类型</li>
            <li>C列：检查项内容</li>
            <li>D列：检查项分类（可选）</li>
            <li>E列：是否必填（是/否，可选）</li>
          </ul>
        </div>
      </el-alert>

      <!-- File upload area -->
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :auto-upload="false"
          :show-file-list="false"
          :accept="'.xlsx,.xls'"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
        >
          <div class="upload-content">
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">
              <p>将 Excel 文件拖拽到此处，或<em>点击选择文件</em></p>
              <p class="upload-hint">支持 .xlsx 和 .xls 格式</p>
            </div>
          </div>
        </el-upload>

        <!-- Selected file info -->
        <div v-if="selectedFile" class="file-info">
          <div class="file-details">
            <el-icon><Document /></el-icon>
            <div class="file-meta">
              <p class="file-name">{{ selectedFile.name }}</p>
              <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
            </div>
          </div>
          <div class="file-actions">
            <el-button size="small" @click="clearFile">
              <el-icon><Delete /></el-icon>
              移除
            </el-button>
          </div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="step-actions">
        <el-button
          type="primary"
          :disabled="!selectedFile"
          :loading="parsing"
          @click="handleParseFile"
        >
          <el-icon><View /></el-icon>
          解析文件
        </el-button>
      </div>
    </el-card>

    <!-- Step 2: Data Preview -->
    <el-card v-if="currentStep === 1" class="step-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">数据预览</span>
          <div class="preview-stats">
            <el-tag type="success">{{ validTemplates.length }} 个有效模板</el-tag>
            <el-tag v-if="invalidRows.length > 0" type="danger">
              {{ invalidRows.length }} 个错误行
            </el-tag>
          </div>
        </div>
      </template>

      <!-- Error summary -->
      <el-alert
        v-if="invalidRows.length > 0"
        title="数据验证错误"
        type="warning"
        :closable="false"
        class="error-alert"
      >
        <div class="error-summary">
          <p>以下行存在错误，将不会被导入：</p>
          <ul>
            <li v-for="error in invalidRows.slice(0, 5)" :key="error.row">
              第 {{ error.row }} 行：{{ error.message }}
            </li>
            <li v-if="invalidRows.length > 5">
              还有 {{ invalidRows.length - 5 }} 个错误...
            </li>
          </ul>
        </div>
      </el-alert>

      <!-- Preview table -->
      <div class="preview-container">
        <h4>预览数据 (前 10 条)</h4>
        <el-table
          :data="validTemplates.slice(0, 10)"
          stripe
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="name" label="模板名称" min-width="200" />
          <el-table-column prop="type" label="模板类型" width="120">
            <template #default="{ row }">
              <el-tag type="primary">{{ row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="itemCount" label="检查项数量" width="120" align="center">
            <template #default="{ row }">
              <el-badge :value="row.items.length" type="primary" />
            </template>
          </el-table-column>
          <el-table-column label="检查项预览" min-width="300">
            <template #default="{ row }">
              <div class="items-preview">
                <div
                  v-for="(item, index) in row.items.slice(0, 3)"
                  :key="index"
                  class="preview-item"
                >
                  <span class="item-content">{{ item.content }}</span>
                  <el-tag v-if="item.category" size="small" type="info">
                    {{ item.category }}
                  </el-tag>
                  <el-tag v-if="item.required" size="small" type="danger">
                    必填
                  </el-tag>
                </div>
                <div v-if="row.items.length > 3" class="more-items">
                  还有 {{ row.items.length - 3 }} 项...
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="validTemplates.length > 10" class="preview-more">
          还有 {{ validTemplates.length - 10 }} 个模板未显示...
        </div>
      </div>

      <!-- Action buttons -->
      <div class="step-actions">
        <el-button @click="goToPreviousStep">
          <el-icon><ArrowLeft /></el-icon>
          上一步
        </el-button>
        <el-button
          type="primary"
          :disabled="validTemplates.length === 0"
          :loading="importing"
          @click="handleImport"
        >
          <el-icon><Upload /></el-icon>
          开始导入 ({{ validTemplates.length }} 个模板)
        </el-button>
      </div>
    </el-card>

    <!-- Step 3: Import Result -->
    <el-card v-if="currentStep === 2" class="step-card" shadow="never">
      <template #header>
        <span class="card-title">导入完成</span>
      </template>

      <!-- Import result -->
      <div class="import-result">
        <div class="result-icon">
          <el-icon
            :class="importResult.success ? 'success-icon' : 'error-icon'"
            size="48"
          >
            <component :is="importResult.success ? 'SuccessFilled' : 'CircleCloseFilled'" />
          </el-icon>
        </div>

        <div class="result-content">
          <h3 :class="importResult.success ? 'success-title' : 'error-title'">
            {{ importResult.success ? '导入成功！' : '导入失败！' }}
          </h3>

          <div class="result-stats">
            <div class="stat-item">
              <span class="stat-label">成功导入：</span>
              <span class="stat-value success">{{ importResult.imported || 0 }} 个模板</span>
            </div>
            <div v-if="importResult.failed > 0" class="stat-item">
              <span class="stat-label">导入失败：</span>
              <span class="stat-value error">{{ importResult.failed }} 个模板</span>
            </div>
          </div>

          <!-- Error details -->
          <div v-if="importResult.errors && importResult.errors.length > 0" class="error-details">
            <h4>错误详情：</h4>
            <ul>
              <li v-for="(error, index) in importResult.errors" :key="index">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="step-actions">
        <el-button @click="handleReset">
          <el-icon><RefreshLeft /></el-icon>
          重新导入
        </el-button>
        <el-button type="primary" @click="handleGoToTemplates">
          <el-icon><Document /></el-icon>
          查看模板列表
        </el-button>
      </div>
    </el-card>

    <!-- Sample file download -->
    <el-card class="sample-card" shadow="never">
      <template #header>
        <span class="card-title">示例文件</span>
      </template>
      <p>如果您不确定文件格式，可以下载示例文件作为参考：</p>
      <el-button @click="downloadSample">
        <el-icon><Download /></el-icon>
        下载示例文件
      </el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadInstance, UploadRawFile } from 'element-plus'
import {
  UploadFilled,
  Document,
  Delete,
  View,
  ArrowLeft,
  Upload,
  Download,
  RefreshLeft,
  SuccessFilled,
  CircleCloseFilled,
} from '@element-plus/icons-vue'
import { importTemplatesFromData, type ImportResult, type TemplateImportData } from '@/api/template'
import * as XLSX from 'xlsx'

// Emits
const emit = defineEmits<{
  goToTemplates: []
}>()

// Refs
const uploadRef = ref<UploadInstance>()

// Reactive data
const currentStep = ref(0)
const selectedFile = ref<File | null>(null)
const parsing = ref(false)
const importing = ref(false)

// Parsed data
interface ParsedTemplate {
  name: string
  type: string
  items: Array<{
    content: string
    category?: string
    required: boolean
  }>
}

interface ParseError {
  row: number
  message: string
}

const parsedData = ref<ParsedTemplate[]>([])
const invalidRows = ref<ParseError[]>([])
const importResult = ref<ImportResult>({
  success: false,
  imported: 0,
  failed: 0,
  errors: [],
})

// Computed
const validTemplates = computed(() => parsedData.value)

// Methods
const handleFileChange = (file: UploadRawFile) => {
  selectedFile.value = file
}

const beforeUpload = (file: UploadRawFile) => {
  // Validate file type
  const isExcel = file.type === 'application/vnd.ms-excel' || 
                  file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  
  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return false
  }

  // Validate file size (10MB)
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB！')
    return false
  }

  return false // Prevent auto upload
}

const clearFile = () => {
  selectedFile.value = null
  uploadRef.value?.clearFiles()
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleParseFile = async () => {
  if (!selectedFile.value) return

  try {
    parsing.value = true
    const data = await parseExcelFile(selectedFile.value)
    
    if (data.templates.length === 0) {
      ElMessage.warning('文件中没有找到有效的模板数据')
      return
    }

    parsedData.value = data.templates
    invalidRows.value = data.errors
    currentStep.value = 1

    ElMessage.success(`成功解析 ${data.templates.length} 个模板`)
  } catch (error) {
    console.error('Failed to parse Excel file:', error)
    ElMessage.error('文件解析失败，请检查文件格式')
  } finally {
    parsing.value = false
  }
}

const parseExcelFile = (file: File): Promise<{
  templates: ParsedTemplate[]
  errors: ParseError[]
}> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        const templates: ParsedTemplate[] = []
        const errors: ParseError[] = []
        const templateMap = new Map<string, ParsedTemplate>()

        // Skip header row if exists
        const startRow = jsonData.length > 0 && 
          Array.isArray(jsonData[0]) &&
          typeof (jsonData[0] as any[])[0] === 'string' && 
          ((jsonData[0] as any[])[0] as string).includes('模板') ? 1 : 0

        for (let i = startRow; i < jsonData.length; i++) {
          const row = jsonData[i] as any[]
          const rowNum = i + 1

          // Skip empty rows
          if (!row || row.every(cell => !cell)) continue

          const templateName = row[0]?.toString()?.trim()
          const templateType = row[1]?.toString()?.trim()
          const itemContent = row[2]?.toString()?.trim()
          const itemCategory = row[3]?.toString()?.trim() || ''
          const itemRequired = row[4]?.toString()?.trim()

          // Validate required fields
          if (!templateName) {
            errors.push({ row: rowNum, message: '模板名称不能为空' })
            continue
          }
          if (!templateType) {
            errors.push({ row: rowNum, message: '模板类型不能为空' })
            continue
          }
          if (!itemContent) {
            errors.push({ row: rowNum, message: '检查项内容不能为空' })
            continue
          }

          // Parse required field
          const required = itemRequired === '是' || itemRequired === 'true' || itemRequired === '1'

          // Get or create template
          const templateKey = `${templateName}_${templateType}`
          let template = templateMap.get(templateKey)
          
          if (!template) {
            template = {
              name: templateName,
              type: templateType,
              items: [],
            }
            templateMap.set(templateKey, template)
            templates.push(template)
          }

          // Add item to template
          template.items.push({
            content: itemContent,
            category: itemCategory,
            required,
          })
        }

        resolve({ templates, errors })
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(file)
  })
}

const goToPreviousStep = () => {
  currentStep.value = 0
}

const handleImport = async () => {
  if (validTemplates.value.length === 0) return

  try {
    importing.value = true

    // Convert parsed templates to the format expected by the API
    const templatesData: TemplateImportData[] = validTemplates.value.map(template => ({
      name: template.name,
      type: template.type,
      items: template.items.map(item => ({
        content: item.content,
        category: item.category || undefined,
        required: item.required || false
      }))
    }))

    // Import via API
    const result = await importTemplatesFromData(templatesData)
    importResult.value = result
    currentStep.value = 2

    if (result.success) {
      ElMessage.success(`成功导入 ${result.imported} 个模板`)
    } else {
      ElMessage.error('导入过程中出现错误')
    }
  } catch (error) {
    console.error('Failed to import templates:', error)
    ElMessage.error('导入失败，请稍后重试')
    importResult.value = {
      success: false,
      imported: 0,
      failed: validTemplates.value.length,
      errors: ['导入过程中发生未知错误'],
    }
    currentStep.value = 2
  } finally {
    importing.value = false
  }
}

const handleReset = () => {
  currentStep.value = 0
  selectedFile.value = null
  parsedData.value = []
  invalidRows.value = []
  importResult.value = {
    success: false,
    imported: 0,
    failed: 0,
    errors: [],
  }
  uploadRef.value?.clearFiles()
}

const handleGoToTemplates = () => {
  emit('goToTemplates')
}

const downloadSample = () => {
  // Create sample data
  const sampleData = [
    ['模板名称', '模板类型', '检查项内容', '检查项分类', '是否必填'],
    ['代码审查模板', '代码审查', '代码是否符合编码规范', '代码质量', '是'],
    ['代码审查模板', '代码审查', '是否有充分的单元测试', '测试覆盖', '是'],
    ['代码审查模板', '代码审查', '代码注释是否清晰', '代码质量', '否'],
    ['部署检查模板', '部署检查', '数据库迁移脚本是否正确', '数据库', '是'],
    ['部署检查模板', '部署检查', '配置文件是否更新', '配置', '是'],
    ['部署检查模板', '部署检查', '监控告警是否配置', '监控', '否'],
  ]

  // Create workbook
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet(sampleData)
  XLSX.utils.book_append_sheet(wb, ws, '模板示例')

  // Download file
  XLSX.writeFile(wb, 'checklist_template_sample.xlsx')
  ElMessage.success('示例文件下载成功')
}
</script>

<style scoped>
.excel-import {
  padding: 20px;
}

.import-header {
  text-align: center;
  margin-bottom: 30px;
}

.import-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.import-steps {
  margin-bottom: 30px;
}

.step-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.preview-stats {
  display: flex;
  gap: 8px;
}

.format-alert {
  margin-bottom: 20px;
}

.format-instructions ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.format-instructions li {
  margin-bottom: 4px;
}

.upload-area {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-content {
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 0 0 8px 0;
  color: #303133;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 6px;
  margin-top: 16px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-meta .file-name {
  margin: 0 0 4px 0;
  font-weight: 500;
  color: #303133;
}

.file-meta .file-size {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
}

.error-alert {
  margin-bottom: 20px;
}

.error-summary ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.preview-container h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.preview-more {
  text-align: center;
  padding: 12px;
  color: #909399;
  font-size: 14px;
}

.items-preview {
  max-height: 120px;
  overflow-y: auto;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.item-content {
  flex: 1;
  color: #303133;
}

.more-items {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.import-result {
  text-align: center;
  padding: 40px 20px;
}

.result-icon {
  margin-bottom: 20px;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.result-content h3 {
  margin: 0 0 20px 0;
}

.success-title {
  color: #67c23a;
}

.error-title {
  color: #f56c6c;
}

.result-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  color: #606266;
}

.stat-value.success {
  color: #67c23a;
  font-weight: 600;
}

.stat-value.error {
  color: #f56c6c;
  font-weight: 600;
}

.error-details {
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.error-details h4 {
  color: #303133;
  margin-bottom: 12px;
}

.error-details ul {
  margin: 0;
  padding-left: 20px;
}

.error-details li {
  margin-bottom: 4px;
  color: #f56c6c;
}

.sample-card {
  margin-top: 20px;
}

.sample-card p {
  margin: 0 0 16px 0;
  color: #606266;
}

:deep(.el-card__header) {
  padding: 16px 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  width: 100%;
  height: auto;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

:deep(.el-steps) {
  margin: 30px 0;
}
</style>
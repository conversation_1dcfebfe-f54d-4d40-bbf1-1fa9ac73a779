<template>
  <div class="template-list">
    <!-- Header with actions -->
    <div class="list-header">
      <div class="header-left">
        <h3>模板列表</h3>
        <el-tag v-if="templates.length > 0" type="info">
          共 {{ templates.length }} 个模板
        </el-tag>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新建模板
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- Search and filter -->
    <div class="search-bar">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索模板名称或类型"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedType"
            placeholder="选择类型"
            clearable
            :loading="typesLoading"
            @change="handleTypeFilter"
          >
            <el-option
              v-for="type in templateTypes"
              :key="type"
              :label="type"
              :value="type"
            />
            <template v-if="templateTypes.length === 0 && !typesLoading">
              <el-option disabled value="" label="暂无类型数据" />
            </template>
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- Template table -->
    <el-table
      v-loading="loading"
      :data="filteredTemplates"
      stripe
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="name" label="模板名称" sortable min-width="180" max-width="300">
        <template #default="{ row }">
          <div class="template-name">
            <el-tooltip :content="row.name" placement="top" :disabled="row.name.length <= 20">
              <strong class="template-name-text">{{ row.name }}</strong>
            </el-tooltip>
            <el-tag v-if="row.version" size="small" type="info" class="version-tag">
              v{{ row.version }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="type" label="类型" sortable width="150">
        <template #default="{ row }">
          <el-tag type="primary">{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="items" label="检查项数量" width="120" align="center">
        <template #default="{ row }">
          <el-badge :value="row.items?.length || 0" type="primary" />
        </template>
      </el-table-column>
      
      <el-table-column prop="createdTime" label="创建时间" sortable width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.createdTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="updatedTime" label="更新时间" sortable width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.updatedTime) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="320" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-tooltip content="查看详情" placement="top">
              <el-button size="small" circle @click="handleView(row)">
                <el-icon><View /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="编辑模板" placement="top">
              <el-button size="small" type="primary" circle @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="复制模板" placement="top">
              <el-button size="small" circle @click="handleDuplicate(row)">
                <el-icon><CopyDocument /></el-icon>
              </el-button>
            </el-tooltip>
            <el-dropdown trigger="click" @command="(command) => handleDropdownAction(command, row)">
              <el-button size="small" circle>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="delete" class="danger-item">
                    <el-icon><Delete /></el-icon>
                    删除模板
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- Empty state -->
    <el-empty
      v-if="!loading && filteredTemplates.length === 0"
      :description="searchQuery || selectedType ? '没有找到匹配的模板' : '暂无模板数据'"
    >
      <el-button v-if="!searchQuery && !selectedType" type="primary" @click="handleCreate">
        创建第一个模板
      </el-button>
    </el-empty>

    <!-- Template form dialog -->
    <TemplateForm
      v-model:visible="formVisible"
      :template="currentTemplate"
      :mode="formMode"
      @success="handleFormSuccess"
    />

    <!-- Template view dialog -->
    <el-dialog
      v-model="viewVisible"
      :title="`查看模板 - ${currentTemplate?.name}`"
      width="60%"
      :before-close="handleViewClose"
    >
      <div v-if="currentTemplate" class="template-view">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">
            {{ currentTemplate.name }}
          </el-descriptions-item>
          <el-descriptions-item label="模板类型">
            <el-tag type="primary">{{ currentTemplate.type }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="版本号">
            {{ currentTemplate.version }}
          </el-descriptions-item>
          <el-descriptions-item label="检查项数量">
            {{ currentTemplate.items?.length || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(currentTemplate.createdTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(currentTemplate.updatedTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin-top: 20px;">检查项列表</h4>
        <el-table :data="currentTemplate.items" stripe style="width: 100%">
          <el-table-column prop="sequence" label="序号" width="80" align="center" />
          <el-table-column prop="content" label="检查内容" min-width="300" />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.category" size="small">{{ row.category }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="required" label="必填" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.required ? 'danger' : 'info'" size="small">
                {{ row.required ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  View,
  Edit,
  Delete,
  CopyDocument,
  MoreFilled,
} from '@element-plus/icons-vue'
import TemplateForm from './TemplateForm.vue'
import {
  getTemplates,
  deleteTemplate,
  duplicateTemplate,
  getTemplateTypes,
  type ChecklistTemplate,
} from '@/api/template'

// Reactive data
const loading = ref(false)
const typesLoading = ref(false)
const templates = ref<ChecklistTemplate[]>([])
const templateTypes = ref<string[]>([])
const searchQuery = ref('')
const selectedType = ref('')
const sortField = ref('')
const sortOrder = ref<'ascending' | 'descending'>('ascending')

// Form dialog
const formVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentTemplate = ref<ChecklistTemplate | null>(null)

// View dialog
const viewVisible = ref(false)

// Computed properties
const filteredTemplates = computed(() => {
  let result = [...templates.value]

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(
      (template) =>
        template.name.toLowerCase().includes(query) ||
        template.type.toLowerCase().includes(query)
    )
  }

  // Filter by type
  if (selectedType.value) {
    result = result.filter((template) => template.type === selectedType.value)
  }

  // Sort
  if (sortField.value) {
    result.sort((a, b) => {
      const aValue = a[sortField.value as keyof ChecklistTemplate]
      const bValue = b[sortField.value as keyof ChecklistTemplate]
      
      let comparison = 0
      if (aValue < bValue) comparison = -1
      if (aValue > bValue) comparison = 1
      
      return sortOrder.value === 'ascending' ? comparison : -comparison
    })
  }

  return result
})

// Methods
const loadTemplates = async () => {
  try {
    loading.value = true
    const response = await getTemplates()
    // 后端返回的数据经过request拦截器处理后，直接是模板数组
    templates.value = Array.isArray(response) ? response : []
  } catch (error) {
    console.error('Failed to load templates:', error)
    ElMessage.error('加载模板列表失败')
  } finally {
    loading.value = false
  }
}

const loadTemplateTypes = async () => {
  try {
    typesLoading.value = true
    const types = await getTemplateTypes()
    // 验证返回的数据格式
    if (Array.isArray(types)) {
      templateTypes.value = types.filter(type => type && typeof type === 'string')
    } else {
      console.warn('Invalid template types data format:', types)
      templateTypes.value = []
    }
  } catch (error) {
    console.error('Failed to load template types:', error)
    ElMessage.warning('加载模板类型失败，类型筛选功能可能不可用')
    templateTypes.value = []
  } finally {
    typesLoading.value = false
  }
}

const handleRefresh = () => {
  loadTemplates()
  loadTemplateTypes()
}

const handleSearch = () => {
  // Search is handled by computed property
}

const handleTypeFilter = () => {
  // Filter is handled by computed property
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'ascending' : 'descending'
}

const handleCreate = () => {
  currentTemplate.value = null
  formMode.value = 'create'
  formVisible.value = true
}

const handleView = (template: ChecklistTemplate) => {
  currentTemplate.value = template
  viewVisible.value = true
}

const handleEdit = (template: ChecklistTemplate) => {
  currentTemplate.value = template
  formMode.value = 'edit'
  formVisible.value = true
}

const handleDuplicate = async (template: ChecklistTemplate) => {
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新模板的名称',
      '复制模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${template.name} - 副本`,
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '模板名称不能为空'
          }
          if (templates.value.some((t) => t.name === value.trim())) {
            return '模板名称已存在'
          }
          return true
        },
      }
    )

    await duplicateTemplate(template.id, newName.trim())
    ElMessage.success('模板复制成功')
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to duplicate template:', error)
    }
  }
}

const handleDropdownAction = (command: string, template: ChecklistTemplate) => {
  switch (command) {
    case 'delete':
      handleDelete(template)
      break
  }
}

const handleDelete = async (template: ChecklistTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteTemplate(template.id)
    ElMessage.success('模板删除成功')
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete template:', error)
    }
  }
}

const handleFormSuccess = () => {
  formVisible.value = false
  loadTemplates()
  loadTemplateTypes()
}

const handleViewClose = () => {
  viewVisible.value = false
  currentTemplate.value = null
}

const formatDateTime = (dateTime: any) => {
  if (!dateTime) return '-'

  // 处理后端返回的复杂时间对象格式
  if (typeof dateTime === 'object' && dateTime.year) {
    const { year, monthValue, dayOfMonth, hour, minute, second } = dateTime
    const date = new Date(year, monthValue - 1, dayOfMonth, hour, minute, second)
    return date.toLocaleString('zh-CN')
  }

  // 处理标准时间字符串或时间戳
  return new Date(dateTime).toLocaleString('zh-CN')
}

// Lifecycle
onMounted(() => {
  loadTemplates()
  loadTemplateTypes()
})
</script>

<style scoped>
.template-list {
  padding: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 8px;
}

.search-bar {
  margin-bottom: 20px;
}

.template-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-name-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.version-tag {
  font-size: 10px;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-start;
}

.template-view {
  max-height: 60vh;
  overflow-y: auto;
}

.template-view h4 {
  color: #303133;
  margin-bottom: 12px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

:deep(.el-empty) {
  padding: 60px 0;
}

:deep(.danger-item) {
  color: #f56c6c;
}

:deep(.danger-item:hover) {
  background-color: #fef0f0;
  color: #f56c6c;
}
</style>
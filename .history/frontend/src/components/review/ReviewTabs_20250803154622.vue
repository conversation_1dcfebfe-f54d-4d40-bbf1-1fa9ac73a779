<template>
  <div class="review-tabs">
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="review-tabs-container">
      <!-- 检查项列表 Tab -->
      <el-tab-pane label="检查项列表" name="checklist" class="tab-pane">
        <template #label>
          <div class="tab-label">
            <el-icon><List /></el-icon>
            <span>检查项列表</span>
            <el-badge v-if="totalItems > 0" :value="totalItems" type="primary" class="tab-badge" />
          </div>
        </template>
        
        <div class="checklist-content">
          <!-- 视图模式选择器 -->
          <ViewModeSelector
            :mode="viewMode"
            :table-configs="tableConfigs"
            :current-config-id="currentTableConfigId"
            :search-text="searchText"
            :status-filter="statusFilter"
            :button-config="buttonConfig"
            @mode-change="$emit('mode-change', $event)"
            @config-change="$emit('config-change', $event)"

            @search-change="$emit('search-change', $event)"
            @status-filter-change="$emit('status-filter-change', $event)"
            @group-by-change="$emit('group-by-change', $event)"
            @filter-command="$emit('filter-command', $event)"
          />

          <!-- 检查项列表 -->
          <div class="items-section">
            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="card-view">
              <div class="items-header">
                <div class="header-title">
                  <h3>检查项列表</h3>
                  <span class="items-count">共 {{ filteredItems.length }} 项</span>
                </div>
              </div>

              <div class="items-container">
                <div class="review-items">
                  <ReviewItemComponent
                    v-for="item in filteredItems"
                    :key="item.itemId"
                    :item="item"
                    :selected="selectedItems.includes(item.itemId)"
                    :readonly="readonly"
                    :button-config="buttonConfig"

                    :current-user="currentUser"
                    :show-traditional-controls="false"
                    @select="(itemId, selected) => $emit('item-select', { itemId, selected })"
                    @status-change="$emit('item-status-change', $event)"
                    @comment-change="$emit('item-comment-change', $event)"
                    @custom-status-change="$emit('custom-status-change', $event)"

                  />
                </div>

                <!-- Empty state -->
                <div v-if="filteredItems.length === 0" class="empty-state">
                  <div class="empty-icon">🔍</div>
                  <div class="empty-text">没有找到匹配的检查项</div>
                  <div class="empty-hint">尝试调整搜索条件或筛选器</div>
                </div>
              </div>
            </div>

            <!-- 表格视图 -->
            <div v-else class="table-view">
              <GroupedTableView
                :items="filteredItems"
                :config="currentTableConfig"
                :selected-items="selectedItems"
                :button-config="buttonConfig"
                @selection-change="$emit('table-selection-change', $event)"
                @sort-change="$emit('table-sort-change', $event)"
                @cell-change="$emit('table-cell-change', $event)"
                @status-change="handleTableStatusChange"
                @export-data="$emit('export-data', $event)"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 缺陷管理 Tab -->
      <el-tab-pane label="缺陷管理" name="defects" class="tab-pane">
        <template #label>
          <div class="tab-label">
            <el-icon><Warning /></el-icon>
            <span>缺陷管理</span>
            <el-badge v-if="defects.length > 0" :value="defects.length" type="danger" class="tab-badge" />
          </div>
        </template>
        
        <div class="defects-content">
          <DefectManager
            :defects="defects"
            @defect-confirm="$emit('defect-confirm', $event)"
            @defect-submit="$emit('defect-submit', $event)"
            @defect-edit="$emit('defect-edit', $event)"
            @defect-delete="$emit('defect-delete', $event)"
            @defects-export="$emit('defects-export', $event)"
            @defects-clear="$emit('defects-clear')"
          />
        </div>
      </el-tab-pane>

      <!-- 评审历史 Tab -->
      <el-tab-pane label="评审历史" name="history" class="tab-pane">
        <template #label>
          <div class="tab-label">
            <el-icon><Clock /></el-icon>
            <span>评审历史</span>
          </div>
        </template>
        
        <div class="history-content">
          <ReviewHistoryView
            :review-id="reviewId"
            :review-items="filteredItems"
          />
        </div>
      </el-tab-pane>

      <!-- 多人协作 Tab (仅在多人评审模式下显示) -->
      <el-tab-pane 
        v-if="isMultiReviewMode" 
        label="多人协作" 
        name="collaboration" 
        class="tab-pane"
      >
        <template #label>
          <div class="tab-label">
            <el-icon><UserFilled /></el-icon>
            <span>多人协作</span>
            <el-badge v-if="collaborators.length > 1" :value="collaborators.length" type="success" class="tab-badge" />
          </div>
        </template>
        
        <div class="collaboration-content">
          <MultiReviewerManager
            :review-id="reviewId"
            :collaborators="collaborators"
            :current-user="currentUser"
            @collaborator-add="$emit('collaborator-add', $event)"
            @collaborator-remove="$emit('collaborator-remove', $event)"
            @assignment-change="$emit('assignment-change', $event)"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { List, Warning, Clock, UserFilled } from '@element-plus/icons-vue'
import {
  type ExtendedReviewItem,
  type TableViewConfig,
  ViewMode
} from '@/types/table-config'
import {
  type StatusButtonConfig,
  type GeneratedDefect
} from '@/types/defect-config'
import ViewModeSelector from './ViewModeSelector.vue'
import ReviewItemComponent from './ReviewItem.vue'
import GroupedTableView from './GroupedTableView.vue'
import DefectManager from './DefectManager.vue'
import ReviewHistoryView from './ReviewHistoryView.vue'
import MultiReviewerManager from './MultiReviewerManager.vue'

// Props
interface Props {
  reviewId: string
  filteredItems: ExtendedReviewItem[]
  selectedItems: string[]
  readonly: boolean
  viewMode: ViewMode
  tableConfigs: TableViewConfig[]
  currentTableConfigId: string
  currentTableConfig: TableViewConfig
  searchText: string
  statusFilter: string
  buttonConfig: StatusButtonConfig
  defects: GeneratedDefect[]
  currentUser: string
  isMultiReviewMode: boolean
  collaborators: any[]
}

const props = withDefaults(defineProps<Props>(), {
  filteredItems: () => [],
  selectedItems: () => [],
  readonly: false,
  viewMode: ViewMode.CARD,
  tableConfigs: () => [],
  currentTableConfigId: 'default',
  searchText: '',
  statusFilter: '',
  defects: () => [],
  currentUser: 'current-user',
  isMultiReviewMode: false,
  collaborators: () => []
})

// Emits
interface Emits {
  (e: 'tab-change', tab: string): void
  (e: 'mode-change', mode: ViewMode): void
  (e: 'config-change', configId: string): void

  (e: 'search-change', text: string): void
  (e: 'status-filter-change', status: string): void
  (e: 'group-by-change', field: string): void
  (e: 'filter-command', command: string): void
  (e: 'item-select', payload: { itemId: string, selected: boolean }): void
  (e: 'item-status-change', payload: { item: any, status: any }): void
  (e: 'item-comment-change', payload: { item: any, comment: string }): void
  (e: 'custom-status-change', payload: any): void

  (e: 'table-selection-change', selectedIds: string[]): void
  (e: 'table-sort-change', payload: { sortBy: string, sortOrder: 'asc' | 'desc' }): void
  (e: 'table-cell-change', payload: { itemId: string, field: string, value: any }): void
  (e: 'table-status-change', payload: { itemId: string, status: any, comment?: string }): void
  (e: 'export-data', config: any): void
  (e: 'defect-confirm', defect: GeneratedDefect): void
  (e: 'defect-submit', defect: GeneratedDefect): void
  (e: 'defect-edit', defect: GeneratedDefect): void
  (e: 'defect-delete', defectId: string): void
  (e: 'defects-export', defects: GeneratedDefect[]): void
  (e: 'defects-clear'): void
  (e: 'collaborator-add', collaborator: any): void
  (e: 'collaborator-remove', collaboratorId: string): void
  (e: 'assignment-change', payload: any): void
}

const emit = defineEmits<Emits>()

// Reactive data
const activeTab = ref('checklist')

// Computed
const totalItems = computed(() => props.filteredItems.length)

// Methods
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  emit('tab-change', tabName)
}

const handleTableStatusChange = (itemId: string, status: any, comment?: string) => {
  emit('table-status-change', { itemId, status, comment })
}
</script>

<style scoped>
.review-tabs {
  height: 100%;
}

.review-tabs-container {
  height: 100%;
}

.review-tabs-container :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow: hidden;
}

.tab-pane {
  height: 100%;
  overflow: auto;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-badge {
  margin-left: 4px;
}

.checklist-content,
.defects-content,
.history-content,
.collaboration-content {
  height: 100%;
  padding: 16px;
}

/* 检查项列表样式 */
.items-section {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-top: 16px;
}

.table-view {
  padding: 0;
  border-radius: 0;
}

.items-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.items-count {
  background: #e1f3ff;
  color: #409eff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.items-container {
  max-height: 600px;
  overflow-y: auto;
}

.review-items {
  display: flex;
  flex-direction: column;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #606266;
}

.empty-hint {
  font-size: 14px;
  color: #909399;
}
</style>

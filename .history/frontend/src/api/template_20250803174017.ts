import request, { type RequestConfig } from '@/utils/request'

// Template related interfaces
export interface ChecklistTemplate {
  id: string
  name: string
  type: string
  version: string
  description?: string
  category?: string
  tags?: string[]
  isActive?: boolean
  createdTime: any // 后端返回复杂时间对象或字符串
  updatedTime: any // 后端返回复杂时间对象或字符串
  items: ChecklistItem[]
}

export interface ChecklistItem {
  id: string
  sequence: number
  content: string
  required: boolean
  category: string
  tags?: string[]
}

export interface CreateTemplateRequest {
  name: string
  type: string
  items: Omit<ChecklistItem, 'id'>[]
}

export interface UpdateTemplateRequest {
  name?: string
  type?: string
  items?: ChecklistItem[]
}

export interface ImportResult {
  success: boolean
  imported: number
  failed: number
  errors?: string[]
}

export interface TemplateImportData {
  name: string
  type: string
  items: TemplateItemData[]
}

export interface TemplateItemData {
  content: string
  category?: string
  required?: boolean
}

export interface TemplateListResponse {
  templates: ChecklistTemplate[]
  total: number
}

// 实际后端返回的原始格式（在request拦截器处理前）
export interface TemplateApiResponse {
  success: boolean
  data: ChecklistTemplate[]
  timestamp: number
}

// Template API service class
class TemplateApiService {
  private readonly baseUrl = '/templates'

  /**
   * Get all templates
   * 注意：由于request拦截器会自动提取data字段，实际返回的是ChecklistTemplate[]
   */
  async getTemplates(config?: RequestConfig): Promise<ChecklistTemplate[]> {
    return request.get(this.baseUrl, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Get templates by type
   */
  async getTemplatesByType(type: string, config?: RequestConfig): Promise<ChecklistTemplate[]> {
    if (!type?.trim()) {
      throw new Error('模板类型不能为空')
    }

    try {
      const response = await request.get(`${this.baseUrl}?type=${encodeURIComponent(type)}`, {
        showLoading: true,
        ...config,
      })

      // 验证响应数据格式
      if (!Array.isArray(response)) {
        console.warn('getTemplatesByType: Invalid response format, expected array:', response)
        return []
      }

      return response
    } catch (error: any) {
      console.error('getTemplatesByType failed:', error)

      // 根据错误类型提供更具体的错误信息
      if (error.code === 'NETWORK_ERROR') {
        throw new Error('网络连接失败，请检查网络连接')
      } else if (error.status === 404) {
        throw new Error(`未找到类型为 "${type}" 的模板`)
      } else if (error.status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      } else {
        throw new Error(error.message || `获取类型为 "${type}" 的模板失败`)
      }
    }
  }

  /**
   * Get template by ID
   */
  async getTemplateById(id: string, config?: RequestConfig): Promise<ChecklistTemplate> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/detail/${id}`, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Get template with full configuration by ID
   */
  async getTemplateWithConfig(id: string, config?: RequestConfig): Promise<any> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/detail/${id}?includeConfig=true`, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Create new template
   */
  async createTemplate(
    template: CreateTemplateRequest,
    config?: RequestConfig,
  ): Promise<ChecklistTemplate> {
    // Validate required fields
    if (!template.name?.trim()) {
      throw new Error('模板名称不能为空')
    }
    if (!template.type?.trim()) {
      throw new Error('模板类型不能为空')
    }
    if (!template.items || template.items.length === 0) {
      throw new Error('检查项不能为空')
    }

    return request.post(this.baseUrl, template, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板创建成功',
      ...config,
    })
  }

  /**
   * Update template
   */
  async updateTemplate(
    id: string,
    template: UpdateTemplateRequest,
    config?: RequestConfig,
  ): Promise<ChecklistTemplate> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    // Validate fields if provided
    if (template.name !== undefined && !template.name.trim()) {
      throw new Error('模板名称不能为空')
    }
    if (template.type !== undefined && !template.type.trim()) {
      throw new Error('模板类型不能为空')
    }
    if (template.items !== undefined && template.items.length === 0) {
      throw new Error('检查项不能为空')
    }

    return request.put(`${this.baseUrl}/${id}`, template, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板更新成功',
      ...config,
    })
  }

  /**
   * Delete template
   */
  async deleteTemplate(id: string, config?: RequestConfig): Promise<void> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.delete(`${this.baseUrl}/${id}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板删除成功',
      ...config,
    })
  }

  /**
   * Import templates from parsed data
   */
  async importTemplatesFromData(templates: TemplateImportData[], config?: RequestConfig): Promise<ImportResult> {
    if (!templates || templates.length === 0) {
      throw new Error('模板数据不能为空')
    }

    return request.post(`${this.baseUrl}/import-data`, templates, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板导入成功',
      ...config,
    })
  }

  /**
   * Export template to Excel
   */
  async exportTemplate(id: string, config?: RequestConfig): Promise<Blob> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/${id}/export`, {
      responseType: 'blob',
      showLoading: true,
      ...config,
    })
  }

  /**
   * Duplicate template
   */
  async duplicateTemplate(id: string, newName: string, config?: RequestConfig): Promise<ChecklistTemplate> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!newName?.trim()) {
      throw new Error('新模板名称不能为空')
    }

    return request.post(`${this.baseUrl}/${id}/duplicate`, { name: newName }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板复制成功',
      ...config,
    })
  }

  /**
   * Get template types
   */
  async getTemplateTypes(config?: RequestConfig): Promise<string[]> {
    try {
      const response = await request.get(`${this.baseUrl}/types`, {
        showLoading: false,
        ...config,
      })

      // 验证响应数据格式
      if (!Array.isArray(response)) {
        console.warn('getTemplateTypes: Invalid response format, expected array:', response)
        return []
      }

      // 过滤无效的类型值
      return response.filter(type => type && typeof type === 'string' && type.trim().length > 0)
    } catch (error: any) {
      console.error('getTemplateTypes failed:', error)

      // 根据错误类型提供更具体的错误信息
      if (error.code === 'NETWORK_ERROR') {
        throw new Error('网络连接失败，请检查网络连接')
      } else if (error.status === 404) {
        throw new Error('模板类型接口不存在，请联系管理员')
      } else if (error.status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      } else {
        throw new Error(error.message || '获取模板类型失败')
      }
    }
  }
}

// Create and export service instance
const templateApi = new TemplateApiService()

// Export individual methods for backward compatibility
export const getTemplates = templateApi.getTemplates.bind(templateApi)
export const getTemplatesByType = templateApi.getTemplatesByType.bind(templateApi)
export const getTemplateById = templateApi.getTemplateById.bind(templateApi)
export const getTemplateWithConfig = templateApi.getTemplateWithConfig.bind(templateApi)
export const createTemplate = templateApi.createTemplate.bind(templateApi)
export const updateTemplate = templateApi.updateTemplate.bind(templateApi)
export const deleteTemplate = templateApi.deleteTemplate.bind(templateApi)
export const importTemplatesFromExcel = templateApi.importTemplatesFromExcel.bind(templateApi)
export const exportTemplate = templateApi.exportTemplate.bind(templateApi)
export const duplicateTemplate = templateApi.duplicateTemplate.bind(templateApi)
export const getTemplateTypes = templateApi.getTemplateTypes.bind(templateApi)

// Export service instance
export default templateApi

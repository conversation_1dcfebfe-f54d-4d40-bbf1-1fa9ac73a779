package com.fasnote.alm.checklist.service;

import com.fasnote.alm.checklist.model.ChecklistItem;
import com.fasnote.alm.checklist.model.ChecklistTemplate;
import com.fasnote.alm.checklist.repository.ChecklistTemplateRepository;
import com.fasnote.alm.checklist.dto.ImportResult;
import com.fasnote.alm.checklist.dto.TemplateImportData;
import com.fasnote.alm.checklist.dto.TemplateItemData;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.UUID;

/**
 * 检查单模板服务类
 * 提供模板管理的业务逻辑，包括创建、更新、删除和查询功能
 */
public class ChecklistTemplateService {
    
    private final ChecklistTemplateRepository templateRepository;

    public ChecklistTemplateService(ChecklistTemplateRepository templateRepository) {
        this.templateRepository = templateRepository;
    }
    
    /**
     * 创建新的检查单模板
     * 
     * @param template 模板对象
     * @return 创建后的模板
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public ChecklistTemplate createTemplate(ChecklistTemplate template) throws IOException {
        // 验证模板数据
        validateTemplate(template);
        
        // 检查模板名称是否已存在
        if (templateRepository.existsByName(template.getName())) {
            throw new IllegalArgumentException("模板名称已存在: " + template.getName());
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        template.setCreatedTime(now);
        template.setUpdatedTime(now);
        
        // 如果没有设置版本，默认为1.0
        if (template.getVersion() == null || template.getVersion().trim().isEmpty()) {
            template.setVersion("1.0");
        }
        
        // 验证和格式化检查项
        formatTemplateItems(template);
        
        // 保存模板
        return templateRepository.save(template);
    }
    
    /**
     * 更新检查单模板
     * 
     * @param id 模板ID
     * @param template 更新的模板数据
     * @return 更新后的模板
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public ChecklistTemplate updateTemplate(String id, ChecklistTemplate template) throws IOException {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        
        // 检查模板是否存在
        Optional<ChecklistTemplate> existingTemplate = templateRepository.findById(id);
        if (!existingTemplate.isPresent()) {
            throw new IllegalArgumentException("模板不存在: " + id);
        }
        
        // 验证模板数据
        validateTemplate(template);
        
        // 检查模板名称是否与其他模板冲突
        if (templateRepository.existsByName(template.getName(), id)) {
            throw new IllegalArgumentException("模板名称已存在: " + template.getName());
        }
        
        // 保留原有的创建时间和ID
        ChecklistTemplate existing = existingTemplate.get();
        template.setId(id);
        template.setCreatedTime(existing.getCreatedTime());
        template.setUpdatedTime(LocalDateTime.now());
        
        // 验证和格式化检查项
        formatTemplateItems(template);
        
        // 保存更新后的模板
        return templateRepository.save(template);
    }
    
    /**
     * 删除检查单模板
     * 
     * @param id 模板ID
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public void deleteTemplate(String id) throws IOException {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        
        // 检查模板是否存在
        if (!templateRepository.existsById(id)) {
            throw new IllegalArgumentException("模板不存在: " + id);
        }
        
        // 删除模板
        templateRepository.deleteById(id);
    }
    
    /**
     * 根据ID查询模板
     * 
     * @param id 模板ID
     * @return 模板的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistTemplate> getTemplateById(String id) throws IOException {
        if (id == null || id.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return templateRepository.findById(id);
    }
    
    /**
     * 获取所有模板
     * 
     * @return 所有模板列表
     * @throws IOException IO异常
     */
    public List<ChecklistTemplate> getAllTemplates() throws IOException {
        return templateRepository.findAll();
    }
    
    /**
     * 根据类型查询模板
     *
     * @param type 模板类型
     * @return 符合条件的模板列表
     * @throws IOException IO异常
     */
    public List<ChecklistTemplate> getTemplatesByType(String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("模板类型不能为空");
        }

        return templateRepository.findByType(type);
    }

    /**
     * 获取所有模板类型
     *
     * @return 所有模板类型列表
     * @throws IOException IO异常
     */
    public List<String> getTemplateTypes() throws IOException {
        List<ChecklistTemplate> allTemplates = templateRepository.findAll();
        return allTemplates.stream()
                .map(ChecklistTemplate::getType)
                .filter(type -> type != null && !type.trim().isEmpty())
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
    
    /**
     * 根据名称查询模板
     * 
     * @param name 模板名称
     * @return 模板的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistTemplate> getTemplateByName(String name) throws IOException {
        if (name == null || name.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return templateRepository.findByName(name);
    }
    
    /**
     * 获取指定类型的最新版本模板
     * 
     * @param type 模板类型
     * @return 最新版本模板的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistTemplate> getLatestTemplateByType(String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return templateRepository.findLatestByType(type);
    }
    
    /**
     * 根据类型和版本查询模板
     * 
     * @param type 模板类型
     * @param version 模板版本
     * @return 模板的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistTemplate> getTemplateByTypeAndVersion(String type, String version) throws IOException {
        if (type == null || type.trim().isEmpty() || version == null || version.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return templateRepository.findByTypeAndVersion(type, version);
    }

    /**
     * 批量导入模板（从前端解析的数据）
     *
     * @param templates 模板列表
     * @return 导入结果
     */
    public ImportResult importTemplatesFromData(List<TemplateImportData> templates) {
        ImportResult result = new ImportResult();
        result.setSuccess(true);
        result.setImported(0);
        result.setFailed(0);
        result.setErrors(new ArrayList<>());

        for (TemplateImportData templateData : templates) {
            try {
                // 检查模板名称是否已存在
                if (templateRepository.existsByName(templateData.getName().trim())) {
                    result.setFailed(result.getFailed() + 1);
                    result.getErrors().add("模板名称已存在: " + templateData.getName());
                    continue;
                }

                // 创建模板对象
                ChecklistTemplate template = new ChecklistTemplate();
                template.setId("template_" + java.util.UUID.randomUUID().toString().replace("-", ""));
                template.setName(templateData.getName().trim());
                template.setType(templateData.getType().trim());
                template.setVersion("1.0");
                template.setCreatedTime(LocalDateTime.now());
                template.setUpdatedTime(LocalDateTime.now());

                // 创建检查项列表
                List<ChecklistItem> items = new ArrayList<>();
                for (int i = 0; i < templateData.getItems().size(); i++) {
                    TemplateItemData itemData = templateData.getItems().get(i);
                    ChecklistItem item = new ChecklistItem();
                    item.setId("item_" + java.util.UUID.randomUUID().toString().replace("-", ""));
                    item.setSequence(i + 1);
                    item.setContent(itemData.getContent().trim());
                    item.setCategory(itemData.getCategory() != null ? itemData.getCategory().trim() : null);
                    item.setRequired(itemData.getRequired() != null ? itemData.getRequired() : false);
                    items.add(item);
                }
                template.setItems(items);

                // 验证并保存模板
                validateTemplate(template);
                templateRepository.save(template);
                result.setImported(result.getImported() + 1);

            } catch (Exception e) {
                result.setFailed(result.getFailed() + 1);
                result.getErrors().add("导入模板 '" + templateData.getName() + "' 失败: " + e.getMessage());
            }
        }

        if (result.getFailed() > 0) {
            result.setSuccess(false);
        }

        return result;
    }

    /**
     * 验证模板数据
     * 
     * @param template 模板对象
     * @throws IllegalArgumentException 验证失败异常
     */
    private void validateTemplate(ChecklistTemplate template) {
        if (template == null) {
            throw new IllegalArgumentException("模板对象不能为空");
        }
        
        // 验证模板名称
        if (template.getName() == null || template.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("模板名称不能为空");
        }
        
        if (template.getName().length() > 100) {
            throw new IllegalArgumentException("模板名称长度不能超过100个字符");
        }
        
        // 验证模板类型
        if (template.getType() == null || template.getType().trim().isEmpty()) {
            throw new IllegalArgumentException("模板类型不能为空");
        }
        
        if (template.getType().length() > 50) {
            throw new IllegalArgumentException("模板类型长度不能超过50个字符");
        }
        
        // 验证版本号
        if (template.getVersion() != null && template.getVersion().length() > 20) {
            throw new IllegalArgumentException("版本号长度不能超过20个字符");
        }
        
        // 验证检查项列表
        if (template.getItems() == null || template.getItems().isEmpty()) {
            throw new IllegalArgumentException("模板必须包含至少一个检查项");
        }
        
        // 验证每个检查项
        for (ChecklistItem item : template.getItems()) {
            validateChecklistItem(item);
        }
    }
    
    /**
     * 验证检查项数据
     * 
     * @param item 检查项对象
     * @throws IllegalArgumentException 验证失败异常
     */
    private void validateChecklistItem(ChecklistItem item) {
        if (item == null) {
            throw new IllegalArgumentException("检查项不能为空");
        }
        
        // 验证检查项内容
        if (item.getContent() == null || item.getContent().trim().isEmpty()) {
            throw new IllegalArgumentException("检查项内容不能为空");
        }
        
        if (item.getContent().length() > 500) {
            throw new IllegalArgumentException("检查项内容长度不能超过500个字符");
        }
        
        // 验证序号
        if (item.getSequence() == null || item.getSequence() < 1) {
            throw new IllegalArgumentException("检查项序号必须为正整数");
        }
        
        // 验证分类
        if (item.getCategory() != null && item.getCategory().length() > 50) {
            throw new IllegalArgumentException("检查项分类长度不能超过50个字符");
        }
    }
    
    /**
     * 格式化模板检查项
     * 
     * @param template 模板对象
     */
    private void formatTemplateItems(ChecklistTemplate template) {
        if (template.getItems() == null || template.getItems().isEmpty()) {
            return;
        }
        
        // 为每个检查项生成ID（如果没有的话）
        for (ChecklistItem item : template.getItems()) {
            if (item.getId() == null || item.getId().trim().isEmpty()) {
                item.setId("item_" + UUID.randomUUID().toString().replace("-", ""));
            }
            
            // 设置默认的required值
            if (item.getRequired() == null) {
                item.setRequired(false);
            }
            
            // 清理空白字符
            if (item.getContent() != null) {
                item.setContent(item.getContent().trim());
            }
            
            if (item.getCategory() != null) {
                item.setCategory(item.getCategory().trim());
            }
        }
        
        // 按序号排序
        template.getItems().sort((item1, item2) -> {
            if (item1.getSequence() == null && item2.getSequence() == null) {
                return 0;
            }
            if (item1.getSequence() == null) {
                return 1;
            }
            if (item2.getSequence() == null) {
                return -1;
            }
            return item1.getSequence().compareTo(item2.getSequence());
        });
    }
}
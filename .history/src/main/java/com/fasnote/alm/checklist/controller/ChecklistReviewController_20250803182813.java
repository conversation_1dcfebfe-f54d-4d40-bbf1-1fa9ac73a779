package com.fasnote.alm.checklist.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasnote.alm.checklist.dto.ApiResponse;
import com.fasnote.alm.checklist.dto.ResponseBuilder;
import com.fasnote.alm.checklist.model.ChecklistReview;
import com.fasnote.alm.checklist.model.ReviewRecord;
import com.fasnote.alm.checklist.model.ReviewStatus;
import com.fasnote.alm.checklist.service.ChecklistReviewService;

/**
 * 检查单评审管理控制器
 * 提供评审管理的 REST API 接口
 */
@RestController
@RequestMapping("/reviews")
public class ChecklistReviewController {

    private final ChecklistReviewService reviewService;

    public ChecklistReviewController(ChecklistReviewService reviewService) {
        this.reviewService = reviewService;
    }
    
    /**
     * 创建评审实例
     * POST /api/reviews
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ChecklistReview>> createReview(@RequestBody Map<String, String> request) {
        try {
            // 参数验证
            String templateId = request.get("templateId");
            if (templateId == null || templateId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("模板ID不能为空");
            }

            ChecklistReview review = reviewService.createReviewFromTemplate(templateId.trim());
            return ResponseBuilder.created(review);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_CREATE_ERROR", "创建评审实例失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "创建评审实例时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取评审实例
     * GET /api/reviews/{id}
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ChecklistReview>> getReviewById(@PathVariable String id) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }

            Optional<ChecklistReview> reviewOpt = reviewService.getReviewById(id.trim());
            if (reviewOpt.isPresent()) {
                return ResponseBuilder.success(reviewOpt.get());
            } else {
                return ResponseBuilder.notFound("评审实例不存在: " + id);
            }
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_READ_ERROR", "读取评审实例失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取评审实例时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 更新评审项状态
     * PUT /api/reviews/{id}/items/{itemId}
     */
    @PutMapping("/{id}/items/{itemId}")
    public ResponseEntity<ApiResponse<ChecklistReview>> updateReviewItemStatus(
            @PathVariable String id,
            @PathVariable String itemId,
            @RequestBody Map<String, String> request) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }

            if (itemId == null || itemId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审项ID不能为空");
            }

            String statusStr = request.get("status");
            String comment = request.get("comment");
            String reviewer = request.get("reviewer");

            if (statusStr == null || statusStr.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审状态不能为空");
            }

            if (reviewer == null || reviewer.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审人不能为空");
            }

            // 解析评审状态
            ReviewStatus status;
            try {
                status = ReviewStatus.fromCode(statusStr.trim());
            } catch (IllegalArgumentException e) {
                return ResponseBuilder.badRequest("无效的评审状态: " + statusStr);
            }

            ChecklistReview updatedReview = reviewService.updateReviewItemStatus(
                id.trim(), itemId.trim(), status, comment, reviewer.trim());
            return ResponseBuilder.success(updatedReview);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_UPDATE_ERROR", "更新评审项状态失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "更新评审项状态时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新评审项状态
     * PUT /api/reviews/{id}/items/batch
     */
    @PutMapping("/{id}/items/batch")
    public ResponseEntity<ApiResponse<ChecklistReview>> batchUpdateReviewItemStatus(
            @PathVariable String id,
            @RequestBody Map<String, Object> request) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }

            @SuppressWarnings("unchecked")
            List<String> itemIds = (List<String>) request.get("itemIds");
            String statusStr = (String) request.get("status");
            String comment = (String) request.get("comment");
            String reviewer = (String) request.get("reviewer");

            if (itemIds == null || itemIds.isEmpty()) {
                return ResponseBuilder.badRequest("评审项ID列表不能为空");
            }

            if (statusStr == null || statusStr.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审状态不能为空");
            }

            if (reviewer == null || reviewer.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审人不能为空");
            }

            // 解析评审状态
            ReviewStatus status;
            try {
                status = ReviewStatus.fromCode(statusStr.trim());
            } catch (IllegalArgumentException e) {
                return ResponseBuilder.badRequest("无效的评审状态: " + statusStr);
            }

            ChecklistReview updatedReview = reviewService.batchUpdateReviewItemStatus(
                id.trim(), itemIds, status, comment, reviewer.trim());
            return ResponseBuilder.success(updatedReview);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_UPDATE_ERROR", "批量更新评审项状态失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "批量更新评审项状态时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取评审历史
     * GET /api/reviews/{id}/history
     */
    @GetMapping("/{id}/history")
    public ResponseEntity<ApiResponse<List<ReviewRecord>>> getReviewHistory(@PathVariable String id) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }

            List<ReviewRecord> history = reviewService.getReviewHistory(id.trim());
            return ResponseBuilder.success(history);
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_HISTORY_READ_ERROR", "读取评审历史失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取评审历史时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 根据类型获取评审列表
     * GET /api/reviews?type={type}
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<ChecklistReview>>> getReviews(@RequestParam(required = false) String type) {
        try {
            List<ChecklistReview> reviews;
            if (type != null && !type.trim().isEmpty()) {
                reviews = reviewService.getReviewsByType(type.trim());
            } else {
                // 如果没有指定类型，返回最近的评审列表
                reviews = reviewService.getRecentReviews(50);
            }

            return ResponseBuilder.success(reviews);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_READ_ERROR", "读取评审列表失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取评审列表时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取评审统计信息
     * GET /api/reviews/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getReviewStatistics() {
        try {
            Map<String, Object> statistics = reviewService.getReviewStatistics();
            return ResponseBuilder.success(statistics);
        } catch (IOException e) {
            return ResponseBuilder.error("STATISTICS_READ_ERROR", "读取评审统计信息失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取评审统计信息时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 完成评审
     * PUT /api/reviews/{id}/complete
     */
    @PutMapping("/{id}/complete")
    public ResponseEntity<ApiResponse<ChecklistReview>> completeReview(
            @PathVariable String id,
            @RequestBody(required = false) Map<String, Object> payload) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }

            // 获取评审实例
            Optional<ChecklistReview> reviewOpt = reviewService.getReviewById(id.trim());
            if (!reviewOpt.isPresent()) {
                return ResponseBuilder.notFound("评审实例不存在: " + id);
            }

            ChecklistReview review = reviewOpt.get();

            // 检查评审状态
            if ("COMPLETED".equals(review.getStatus())) {
                return ResponseBuilder.error("REVIEW_ALREADY_COMPLETED", "评审已经完成", HttpStatus.BAD_REQUEST);
            }

            // 完成评审
            ChecklistReview completedReview = reviewService.completeReview(id.trim(), payload);
            return ResponseBuilder.success(completedReview);

        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_COMPLETE_ERROR", "完成评审失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "完成评审时发生未知错误: " + e.getMessage());
        }
    }
}
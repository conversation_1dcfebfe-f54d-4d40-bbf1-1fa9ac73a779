package com.fasnote.alm.checklist.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasnote.alm.checklist.model.ChecklistReview;
import com.fasnote.alm.checklist.model.ReviewRecord;
import com.fasnote.alm.checklist.model.ReviewStatus;
import com.fasnote.alm.checklist.service.ChecklistReviewService;

/**
 * 检查单评审管理控制器
 * 提供评审管理的 REST API 接口
 */
@RestController
@RequestMapping("/reviews")
public class ChecklistReviewController {

    private final ChecklistReviewService reviewService;

    public ChecklistReviewController(ChecklistReviewService reviewService) {
        this.reviewService = reviewService;
    }
    
    /**
     * 创建评审实例
     * POST /api/reviews
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createReview(@RequestBody Map<String, String> request) {
        try {
            // 参数验证
            String templateId = request.get("templateId");
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            ChecklistReview review = reviewService.createReviewFromTemplate(templateId.trim());
            Map<String, Object> response = createSuccessResponse(review);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("REVIEW_CREATE_ERROR", 
                "创建评审实例失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "创建评审实例时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 获取评审实例
     * GET /api/reviews/{id}
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getReviewById(@PathVariable String id) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            Optional<ChecklistReview> reviewOpt = reviewService.getReviewById(id.trim());
            if (reviewOpt.isPresent()) {
                Map<String, Object> response = createSuccessResponse(reviewOpt.get());
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> errorResponse = createErrorResponse("REVIEW_NOT_FOUND", 
                    "评审实例不存在: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("REVIEW_READ_ERROR", 
                "读取评审实例失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取评审实例时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 更新评审项状态
     * PUT /api/reviews/{id}/items/{itemId}
     */
    @PutMapping("/{id}/items/{itemId}")
    public ResponseEntity<Map<String, Object>> updateReviewItemStatus(
            @PathVariable String id, 
            @PathVariable String itemId,
            @RequestBody Map<String, String> request) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (itemId == null || itemId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审项ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            String statusStr = request.get("status");
            String comment = request.get("comment");
            String reviewer = request.get("reviewer");
            
            if (statusStr == null || statusStr.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审状态不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (reviewer == null || reviewer.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审人不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            // 解析评审状态
            ReviewStatus status;
            try {
                status = ReviewStatus.fromCode(statusStr.trim());
            } catch (IllegalArgumentException e) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "无效的评审状态: " + statusStr);
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            ChecklistReview updatedReview = reviewService.updateReviewItemStatus(
                id.trim(), itemId.trim(), status, comment, reviewer.trim());
            Map<String, Object> response = createSuccessResponse(updatedReview);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("REVIEW_UPDATE_ERROR", 
                "更新评审项状态失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "更新评审项状态时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 批量更新评审项状态
     * PUT /api/reviews/{id}/items/batch
     */
    @PutMapping("/{id}/items/batch")
    public ResponseEntity<Map<String, Object>> batchUpdateReviewItemStatus(
            @PathVariable String id,
            @RequestBody Map<String, Object> request) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            @SuppressWarnings("unchecked")
            List<String> itemIds = (List<String>) request.get("itemIds");
            String statusStr = (String) request.get("status");
            String comment = (String) request.get("comment");
            String reviewer = (String) request.get("reviewer");
            
            if (itemIds == null || itemIds.isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审项ID列表不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (statusStr == null || statusStr.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审状态不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (reviewer == null || reviewer.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审人不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            // 解析评审状态
            ReviewStatus status;
            try {
                status = ReviewStatus.fromCode(statusStr.trim());
            } catch (IllegalArgumentException e) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "无效的评审状态: " + statusStr);
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            ChecklistReview updatedReview = reviewService.batchUpdateReviewItemStatus(
                id.trim(), itemIds, status, comment, reviewer.trim());
            Map<String, Object> response = createSuccessResponse(updatedReview);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("REVIEW_UPDATE_ERROR", 
                "批量更新评审项状态失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "批量更新评审项状态时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 获取评审历史
     * GET /api/reviews/{id}/history
     */
    @GetMapping("/{id}/history")
    public ResponseEntity<Map<String, Object>> getReviewHistory(@PathVariable String id) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "评审ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            List<ReviewRecord> history = reviewService.getReviewHistory(id.trim());
            Map<String, Object> response = createSuccessResponse(history);
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("REVIEW_HISTORY_READ_ERROR", 
                "读取评审历史失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取评审历史时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 根据类型获取评审列表
     * GET /api/reviews?type={type}
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getReviews(@RequestParam(required = false) String type) {
        try {
            List<ChecklistReview> reviews;
            if (type != null && !type.trim().isEmpty()) {
                reviews = reviewService.getReviewsByType(type.trim());
            } else {
                // 如果没有指定类型，返回最近的评审列表
                reviews = reviewService.getRecentReviews(50);
            }
            
            Map<String, Object> response = createSuccessResponse(reviews);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("REVIEW_READ_ERROR", 
                "读取评审列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取评审列表时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 获取评审统计信息
     * GET /api/reviews/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getReviewStatistics() {
        try {
            Map<String, Object> statistics = reviewService.getReviewStatistics();
            Map<String, Object> response = createSuccessResponse(statistics);
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("STATISTICS_READ_ERROR", 
                "读取评审统计信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取评审统计信息时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 完成评审
     * PUT /api/reviews/{id}/complete
     */
    @PutMapping("/{id}/complete")
    public ResponseEntity<Map<String, Object>> completeReview(
            @PathVariable String id,
            @RequestBody(required = false) Map<String, Object> payload) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER",
                    "评审ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取评审实例
            Optional<ChecklistReview> reviewOpt = reviewService.getReviewById(id.trim());
            if (!reviewOpt.isPresent()) {
                Map<String, Object> errorResponse = createErrorResponse("REVIEW_NOT_FOUND",
                    "评审实例不存在: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }

            ChecklistReview review = reviewOpt.get();

            // 检查评审状态
            if ("COMPLETED".equals(review.getStatus())) {
                Map<String, Object> errorResponse = createErrorResponse("REVIEW_ALREADY_COMPLETED",
                    "评审已经完成");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 完成评审
            ChecklistReview completedReview = reviewService.completeReview(id.trim(), payload);
            Map<String, Object> response = createSuccessResponse(completedReview);
            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("REVIEW_COMPLETE_ERROR",
                "完成评审失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR",
                "完成评审时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorCode, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", errorCode);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
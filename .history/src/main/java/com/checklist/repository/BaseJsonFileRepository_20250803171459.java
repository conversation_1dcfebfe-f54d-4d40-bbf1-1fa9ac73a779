package com.checklist.repository;

import com.checklist.util.FileUtil;
import com.checklist.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * JSON 文件存储仓库基础实现类
 * 提供通用的 CRUD 操作实现
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
public abstract class BaseJsonFileRepository<T, ID> implements JsonFileRepository<T, ID> {

    private static final Logger logger = LoggerFactory.getLogger(BaseJsonFileRepository.class);

    // 文件锁映射，用于控制并发访问
    private static final Map<String, Object> fileLocks = new ConcurrentHashMap<>();
    
    protected final String dataDirectory;
    protected final String resolvedDataDirectory;
    protected final String fileExtension;
    protected final Class<T> entityClass;
    protected final TypeReference<List<T>> listTypeReference;

    /**
     * 构造函数
     *
     * @param dataDirectory 数据目录
     * @param entityClass 实体类
     * @param listTypeReference 列表类型引用
     */
    protected BaseJsonFileRepository(String dataDirectory, Class<T> entityClass,
                                   TypeReference<List<T>> listTypeReference) {
        this.dataDirectory = dataDirectory;
        this.resolvedDataDirectory = resolveDataDirectory(dataDirectory);
        this.fileExtension = ".json";
        this.entityClass = entityClass;
        this.listTypeReference = listTypeReference;

        // 确保数据目录存在
        try {
            FileUtil.createDirectories(this.resolvedDataDirectory);
        } catch (IOException e) {
            throw new RuntimeException("无法创建数据目录: " + this.resolvedDataDirectory +
                " (原始路径: " + dataDirectory + ")", e);
        }
    }
    
    /**
     * 获取实体ID的抽象方法
     * 
     * @param entity 实体
     * @return 实体ID
     */
    protected abstract ID getEntityId(T entity);
    
    /**
     * 设置实体ID的抽象方法
     * 
     * @param entity 实体
     * @param id 要设置的ID
     */
    protected abstract void setEntityId(T entity, ID id);
    
    /**
     * 生成新ID的抽象方法
     *
     * @return 新的ID
     */
    protected abstract ID generateNewId();

    /**
     * 解析数据目录路径
     *
     * @param dataDirectory 原始数据目录路径
     * @return 解析后的绝对路径
     */
    private String resolveDataDirectory(String dataDirectory) {
        if (dataDirectory == null || dataDirectory.trim().isEmpty()) {
            throw new IllegalArgumentException("数据目录路径不能为空");
        }

        Path path = Paths.get(dataDirectory);

        // 如果是绝对路径，直接返回
        if (path.isAbsolute()) {
            return path.toString();
        }

        // 首先尝试基于类路径查找项目根目录
        Path projectRoot = findProjectRootFromClassPath();
        if (projectRoot != null) {
            Path resolvedPath = projectRoot.resolve(dataDirectory);
            if (java.nio.file.Files.exists(resolvedPath) ||
                java.nio.file.Files.exists(resolvedPath.getParent())) {
                return resolvedPath.toString();
            }
        }

        // 如果基于类路径找不到，尝试基于工作目录
        String workingDir = System.getProperty("user.dir");
        Path workingPath = Paths.get(workingDir);
        Path resolvedPath = workingPath.resolve(dataDirectory);

        // 检查路径是否存在，如果不存在尝试其他可能的根目录
        if (!java.nio.file.Files.exists(resolvedPath)) {
            // 尝试查找项目根目录（包含 src 目录的目录）
            Path projectRootFromWorking = findProjectRoot(workingPath);
            if (projectRootFromWorking != null) {
                Path alternativePath = projectRootFromWorking.resolve(dataDirectory);
                if (java.nio.file.Files.exists(alternativePath) ||
                    java.nio.file.Files.exists(alternativePath.getParent())) {
                    resolvedPath = alternativePath;
                }
            }
        }

        return resolvedPath.toString();
    }

    /**
     * 基于类路径查找项目根目录
     *
     * @return 项目根目录，如果未找到返回null
     */
    private Path findProjectRootFromClassPath() {
        try {
            // 获取当前类的类路径
            String className = this.getClass().getName().replace('.', '/') + ".class";
            java.net.URL classUrl = this.getClass().getClassLoader().getResource(className);

            if (classUrl != null) {
                String classPath = classUrl.getPath();

                // 如果是从文件系统加载的类（开发环境）
                if (classPath.contains("/target/classes/") || classPath.contains("/bin/")) {
                    // 找到 target/classes 或 bin 目录，向上查找项目根目录
                    Path classFilePath = Paths.get(classPath);
                    Path current = classFilePath;

                    while (current != null) {
                        if (current.getFileName() != null &&
                            (current.getFileName().toString().equals("classes") ||
                             current.getFileName().toString().equals("bin"))) {
                            // 向上两级到项目根目录
                            Path projectRoot = current.getParent().getParent();
                            if (isProjectRoot(projectRoot)) {
                                return projectRoot;
                            }
                        }
                        current = current.getParent();
                    }
                }
            }

            // 如果基于类路径找不到，尝试基于代码源位置
            java.security.ProtectionDomain pd = this.getClass().getProtectionDomain();
            if (pd != null && pd.getCodeSource() != null) {
                java.net.URL codeSourceUrl = pd.getCodeSource().getLocation();
                if (codeSourceUrl != null) {
                    Path codeSourcePath = Paths.get(codeSourceUrl.toURI());
                    return findProjectRoot(codeSourcePath);
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }

        return null;
    }

    /**
     * 检查是否为项目根目录
     *
     * @param path 要检查的路径
     * @return 是否为项目根目录
     */
    private boolean isProjectRoot(Path path) {
        if (path == null || !java.nio.file.Files.exists(path)) {
            return false;
        }

        // 检查是否包含典型的项目标识文件/目录
        return java.nio.file.Files.exists(path.resolve("src")) ||
               java.nio.file.Files.exists(path.resolve("pom.xml")) ||
               java.nio.file.Files.exists(path.resolve("build.gradle")) ||
               java.nio.file.Files.exists(path.resolve("plugin.xml")) ||
               java.nio.file.Files.exists(path.resolve("data"));
    }

    /**
     * 查找项目根目录
     *
     * @param startPath 开始查找的路径
     * @return 项目根目录，如果未找到返回null
     */
    private Path findProjectRoot(Path startPath) {
        Path current = startPath;
        while (current != null) {
            if (isProjectRoot(current)) {
                return current;
            }
            current = current.getParent();
        }
        return null;
    }
    
    /**
     * 获取文件路径
     *
     * @param id 实体ID
     * @return 文件路径
     */
    protected String getFilePath(ID id) {
        return resolvedDataDirectory + "/" + id + fileExtension;
    }
    
    /**
     * 获取文件锁对象
     * 
     * @param filePath 文件路径
     * @return 锁对象
     */
    protected Object getFileLock(String filePath) {
        return fileLocks.computeIfAbsent(filePath, k -> new Object());
    }
    
    @Override
    public T save(T entity) throws IOException {
        if (entity == null) {
            throw new IllegalArgumentException("实体不能为空");
        }
        
        ID id = getEntityId(entity);
        if (id == null) {
            id = generateNewId();
            setEntityId(entity, id);
        }
        
        String filePath = getFilePath(id);
        Object lock = getFileLock(filePath);
        
        synchronized (lock) {
            try {
                String jsonContent = JsonUtil.toJson(entity);
                FileUtil.safeWriteFile(filePath, jsonContent);
                return entity;
            } catch (Exception e) {
                throw new IOException("保存实体失败: " + id, e);
            }
        }
    }
    
    @Override
    public Optional<T> findById(ID id) throws IOException {
        if (id == null) {
            return Optional.empty();
        }
        
        String filePath = getFilePath(id);
        if (!FileUtil.fileExists(filePath)) {
            return Optional.empty();
        }
        
        Object lock = getFileLock(filePath);
        
        synchronized (lock) {
            try {
                String jsonContent = FileUtil.readFileWithLock(filePath);
                if (jsonContent == null || jsonContent.trim().isEmpty()) {
                    return Optional.empty();
                }
                
                T entity = JsonUtil.fromJson(jsonContent, entityClass);
                return Optional.ofNullable(entity);
            } catch (Exception e) {
                throw new IOException("读取实体失败: " + id, e);
            }
        }
    }
    
    @Override
    public List<T> findAll() throws IOException {
        List<T> result = new ArrayList<>();

        try {
            // 获取数据目录下所有JSON文件
            java.nio.file.Path dataPath = java.nio.file.Paths.get(resolvedDataDirectory);
            if (!java.nio.file.Files.exists(dataPath)) {
                // 记录调试信息
                logger.warn("数据目录不存在: {} (原始路径: {})", resolvedDataDirectory, dataDirectory);
                return result;
            }

            try (java.nio.file.DirectoryStream<java.nio.file.Path> stream =
                 java.nio.file.Files.newDirectoryStream(dataPath, "*" + fileExtension)) {

                for (java.nio.file.Path filePath : stream) {
                    String filePathStr = filePath.toString();
                    Object lock = getFileLock(filePathStr);

                    synchronized (lock) {
                        try {
                            String jsonContent = FileUtil.readFileWithLock(filePathStr);
                            if (jsonContent != null && !jsonContent.trim().isEmpty()) {
                                T entity = JsonUtil.fromJson(jsonContent, entityClass);
                                if (entity != null) {
                                    result.add(entity);
                                }
                            }
                        } catch (Exception e) {
                            // 记录错误但继续处理其他文件
                            logger.error("读取文件失败: {}, 错误: {}", filePath, e.getMessage(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("读取所有实体失败", e);
        }

        return result;
    }
    
    @Override
    public boolean existsById(ID id) throws IOException {
        if (id == null) {
            return false;
        }
        
        String filePath = getFilePath(id);
        return FileUtil.fileExists(filePath);
    }
    
    @Override
    public void deleteById(ID id) throws IOException {
        if (id == null) {
            return;
        }
        
        String filePath = getFilePath(id);
        Object lock = getFileLock(filePath);
        
        synchronized (lock) {
            try {
                if (FileUtil.fileExists(filePath)) {
                    // 创建备份后删除
                    FileUtil.createBackup(filePath);
                    FileUtil.deleteFile(filePath);
                }
            } catch (Exception e) {
                throw new IOException("删除实体失败: " + id, e);
            }
        }
    }
    
    @Override
    public void delete(T entity) throws IOException {
        if (entity == null) {
            return;
        }
        
        ID id = getEntityId(entity);
        deleteById(id);
    }
    
    @Override
    public void deleteAll() throws IOException {
        try {
            java.nio.file.Path dataPath = java.nio.file.Paths.get(resolvedDataDirectory);
            if (!java.nio.file.Files.exists(dataPath)) {
                return;
            }
            
            try (java.nio.file.DirectoryStream<java.nio.file.Path> stream = 
                 java.nio.file.Files.newDirectoryStream(dataPath, "*" + fileExtension)) {
                
                for (java.nio.file.Path filePath : stream) {
                    try {
                        // 创建备份后删除
                        FileUtil.createBackup(filePath.toString());
                        FileUtil.deleteFile(filePath.toString());
                    } catch (Exception e) {
                        logger.error("删除文件失败: {}, 错误: {}", filePath, e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("删除所有实体失败", e);
        }
    }
    
    @Override
    public long count() throws IOException {
        try {
            java.nio.file.Path dataPath = java.nio.file.Paths.get(resolvedDataDirectory);
            if (!java.nio.file.Files.exists(dataPath)) {
                return 0;
            }
            
            long count = 0;
            try (java.nio.file.DirectoryStream<java.nio.file.Path> stream = 
                 java.nio.file.Files.newDirectoryStream(dataPath, "*" + fileExtension)) {
                
                for (java.nio.file.Path filePath : stream) {
                    count++;
                }
            }
            
            return count;
        } catch (Exception e) {
            throw new IOException("统计实体数量失败", e);
        }
    }
    
    /**
     * 根据条件查找实体
     * 
     * @param predicate 查找条件
     * @return 符合条件的实体列表
     * @throws IOException IO异常
     */
    public List<T> findBy(Function<T, Boolean> predicate) throws IOException {
        List<T> allEntities = findAll();
        List<T> result = new ArrayList<>();
        
        for (T entity : allEntities) {
            if (predicate.apply(entity)) {
                result.add(entity);
            }
        }
        
        return result;
    }
    
    /**
     * 根据条件查找第一个实体
     * 
     * @param predicate 查找条件
     * @return 符合条件的第一个实体的Optional包装
     * @throws IOException IO异常
     */
    public Optional<T> findFirstBy(Function<T, Boolean> predicate) throws IOException {
        List<T> allEntities = findAll();
        
        for (T entity : allEntities) {
            if (predicate.apply(entity)) {
                return Optional.of(entity);
            }
        }
        
        return Optional.empty();
    }

    /**
     * 获取路径调试信息
     *
     * @return 路径调试信息
     */
    public String getPathDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("路径调试信息:\n");
        info.append("- 原始数据目录: ").append(dataDirectory).append("\n");
        info.append("- 解析后数据目录: ").append(resolvedDataDirectory).append("\n");
        info.append("- 当前工作目录: ").append(System.getProperty("user.dir")).append("\n");

        // 添加类路径信息
        Path classPathRoot = findProjectRootFromClassPath();
        info.append("- 基于类路径的项目根目录: ").append(classPathRoot != null ? classPathRoot.toString() : "未找到").append("\n");

        // 添加工作目录项目根目录信息
        Path workingRoot = findProjectRoot(Paths.get(System.getProperty("user.dir")));
        info.append("- 基于工作目录的项目根目录: ").append(workingRoot != null ? workingRoot.toString() : "未找到").append("\n");

        info.append("- 数据目录是否存在: ").append(java.nio.file.Files.exists(Paths.get(resolvedDataDirectory))).append("\n");

        try {
            java.nio.file.Path dataPath = Paths.get(resolvedDataDirectory);
            if (java.nio.file.Files.exists(dataPath)) {
                long fileCount = 0;
                try (java.nio.file.DirectoryStream<java.nio.file.Path> stream =
                     java.nio.file.Files.newDirectoryStream(dataPath, "*" + fileExtension)) {
                    for (java.nio.file.Path filePath : stream) {
                        fileCount++;
                    }
                }
                info.append("- JSON文件数量: ").append(fileCount).append("\n");
            }
        } catch (IOException e) {
            info.append("- 读取目录时出错: ").append(e.getMessage()).append("\n");
        }

        return info.toString();
    }
}